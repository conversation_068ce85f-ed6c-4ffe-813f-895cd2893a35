/* 
 * 响应式样式 - License激活页面
 * 多设备和多分辨率适配
 */

/* 基础响应式断点 */
/* 
 * 断点定义：
 * - xs: 0-575px (手机竖屏)
 * - sm: 576-767px (手机横屏)
 * - md: 768-991px (平板竖屏)
 * - lg: 992-1199px (平板横屏/小笔记本)
 * - xl: 1200-1399px (桌面)
 * - xxl: 1400px+ (大桌面)
 */

/* 超小屏幕 (手机竖屏) */
@media (max-width: 575px) {
  .license-activation-page {
    padding: var(--spacing-sm);
    align-items: flex-start;
    padding-top: var(--spacing-md);
  }
  
  .activation-container {
    min-width: 280px;
    max-width: 100%;
    border-radius: var(--border-radius-medium);
  }
  
  .activation-header {
    padding: var(--spacing-md) var(--spacing-sm);
  }
  
  .activation-title {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .activation-title h1 {
    font-size: 20px;
    line-height: 24px;
  }
  
  .activation-subtitle {
    font-size: 13px;
    line-height: 18px;
  }
  
  .activation-form {
    padding: var(--spacing-md) var(--spacing-sm);
    gap: var(--spacing-sm);
  }
  
  .form-group {
    margin-bottom: var(--spacing-sm);
  }
  
  .input-license-key,
  .input-text {
    font-size: 16px; /* 防止iOS缩放 */
    min-height: 48px;
  }
  
  .activation-status {
    margin: 0 var(--spacing-sm);
    padding: var(--spacing-sm);
  }
  
  .primary-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .secondary-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
    align-items: center;
  }
  
  .activation-actions {
    padding: var(--spacing-md) var(--spacing-sm);
  }
  
  .activation-footer {
    padding: var(--spacing-sm);
  }
  
  .footer-support {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  /* 通知适配 */
  .notification-container {
    top: var(--spacing-xs);
    right: var(--spacing-xs);
    left: var(--spacing-xs);
    max-width: none;
  }
  
  .notification {
    max-width: none;
  }
}

/* 小屏幕 (手机横屏) */
@media (min-width: 576px) and (max-width: 767px) {
  .license-activation-page {
    padding: var(--spacing-sm);
  }
  
  .activation-container {
    max-width: 540px;
  }
  
  .activation-header {
    padding: var(--spacing-lg) var(--spacing-md);
  }
  
  .activation-title h1 {
    font-size: 24px;
  }
  
  .activation-form {
    padding: var(--spacing-lg) var(--spacing-md);
  }
  
  .primary-actions {
    flex-direction: row;
  }
  
  .secondary-actions {
    flex-direction: row;
    justify-content: center;
  }
}

/* 中等屏幕 (平板竖屏) */
@media (min-width: 768px) and (max-width: 991px) {
  .activation-container {
    max-width: 720px;
  }
  
  .activation-header {
    padding: var(--spacing-xl) var(--spacing-lg);
  }
  
  .activation-form {
    padding: var(--spacing-xl) var(--spacing-lg);
  }
  
  .activation-actions {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-xl);
  }
  
  /* 平板优化的触控目标 */
  .button-primary,
  .button-secondary {
    min-height: 44px;
    padding: 12px var(--spacing-lg);
  }
  
  .input-license-key,
  .input-text {
    min-height: 44px;
  }
}

/* 大屏幕 (平板横屏/小笔记本) */
@media (min-width: 992px) and (max-width: 1199px) {
  .activation-container {
    max-width: 480px;
  }
  
  /* 标准桌面交互 */
  .button-primary,
  .button-secondary {
    min-height: 32px;
  }
  
  .input-license-key,
  .input-text {
    min-height: 36px;
  }
}

/* 超大屏幕 (桌面) */
@media (min-width: 1200px) and (max-width: 1399px) {
  .license-activation-page {
    padding: var(--spacing-lg);
  }
  
  .activation-container {
    max-width: var(--content-width-max);
  }
}

/* 超超大屏幕 (大桌面) */
@media (min-width: 1400px) {
  .license-activation-page {
    padding: var(--spacing-xl);
  }
  
  .activation-container {
    max-width: 520px;
  }
  
  /* 大屏幕可以显示更多信息 */
  .activation-help-panel {
    display: block;
    position: fixed;
    right: var(--spacing-xl);
    top: 50%;
    transform: translateY(-50%);
    width: 280px;
    background: var(--background-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-large);
    padding: var(--spacing-lg);
    box-shadow: var(--elevation-2);
  }
}

/* 高度适配 */
@media (max-height: 600px) {
  .license-activation-page {
    align-items: flex-start;
    padding-top: var(--spacing-sm);
  }
  
  .activation-header {
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  .activation-title .icon-hero {
    width: var(--icon-size-large);
    height: var(--icon-size-large);
  }
  
  .activation-form {
    padding: var(--spacing-md) var(--spacing-lg);
    gap: var(--spacing-sm);
  }
  
  .form-group {
    margin-bottom: var(--spacing-sm);
  }
  
  .activation-actions {
    padding: var(--spacing-md) var(--spacing-lg);
  }
}

@media (max-height: 480px) {
  .activation-title {
    flex-direction: row;
    gap: var(--spacing-sm);
  }
  
  .activation-title .icon-hero {
    width: var(--icon-size-medium);
    height: var(--icon-size-medium);
  }
  
  .activation-title h1 {
    font-size: var(--font-size-h3);
  }
  
  .activation-subtitle {
    display: none;
  }
  
  .activation-footer {
    display: none;
  }
}

/* DPI/分辨率适配 */
@media (min-resolution: 144dpi) {
  /* 高DPI显示器优化 */
  .icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
  
  .activation-container {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }
}

@media (min-resolution: 192dpi) {
  /* 超高DPI显示器优化 */
  .button-primary,
  .button-secondary {
    border-width: 0.5px;
  }
  
  .input-license-key,
  .input-text {
    border-width: 0.5px;
  }
}

/* 宽屏适配 */
@media (min-aspect-ratio: 16/9) and (min-width: 1200px) {
  .license-activation-page {
    background: radial-gradient(ellipse at center, var(--background-secondary) 0%, var(--background-tertiary) 100%);
  }
  
  .activation-container {
    max-width: 600px;
  }
}

/* 超宽屏适配 */
@media (min-aspect-ratio: 21/9) and (min-width: 1400px) {
  .activation-container {
    max-width: 480px;
  }
  
  /* 可以在两侧显示装饰性内容 */
  .license-activation-page::before,
  .license-activation-page::after {
    content: '';
    position: fixed;
    top: 0;
    bottom: 0;
    width: 200px;
    background: linear-gradient(to bottom, transparent, var(--primary-color), transparent);
    opacity: 0.05;
    pointer-events: none;
  }
  
  .license-activation-page::before {
    left: 0;
  }
  
  .license-activation-page::after {
    right: 0;
  }
}

/* 横屏手机特殊处理 */
@media (max-height: 500px) and (orientation: landscape) {
  .license-activation-page {
    padding: var(--spacing-xs);
    overflow-y: auto;
  }
  
  .activation-container {
    margin: var(--spacing-xs) auto;
  }
  
  .activation-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .activation-title {
    flex-direction: row;
    gap: var(--spacing-sm);
  }
  
  .activation-title .icon-hero {
    width: var(--icon-size-medium);
    height: var(--icon-size-medium);
  }
  
  .activation-form {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .activation-status {
    margin: 0 var(--spacing-md);
    padding: var(--spacing-sm);
  }
  
  .activation-actions {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .activation-footer {
    padding: var(--spacing-xs) var(--spacing-md);
  }
}

/* 容器查询（实验性支持） */
@supports (container-type: inline-size) {
  .activation-container {
    container-type: inline-size;
  }
  
  @container (max-width: 400px) {
    .primary-actions {
      flex-direction: column;
    }
    
    .secondary-actions {
      flex-direction: column;
      gap: var(--spacing-xs);
    }
  }
  
  @container (min-width: 500px) {
    .activation-form {
      padding: var(--spacing-xl);
    }
  }
}

/* 打印适配 */
@media print {
  .license-activation-page {
    background: white;
    padding: 0;
    min-height: auto;
  }
  
  .activation-container {
    max-width: none;
    box-shadow: none;
    border: 1px solid black;
    page-break-inside: avoid;
  }
  
  .activation-actions,
  .activation-footer,
  .notification-container,
  .modal-container,
  .drop-overlay {
    display: none;
  }
  
  .activation-header,
  .activation-form,
  .activation-status {
    page-break-inside: avoid;
  }
}

/* 环境适配 */
@media (hover: none) {
  /* 触控设备：移除悬停效果 */
  .button-primary:hover,
  .button-secondary:hover,
  .button-text:hover,
  .input-license-key:hover,
  .input-text:hover {
    transform: none;
    box-shadow: none;
  }
}

@media (hover: hover) {
  /* 精确指针设备：增强悬停效果 */
  .button-primary:hover {
    transform: translateY(-1px);
  }
  
  .button-secondary:hover {
    transform: translateY(-0.5px);
  }
}

/* 指针精度适配 */
@media (pointer: coarse) {
  /* 粗糙指针（触控）：增大交互目标 */
  .button-primary,
  .button-secondary {
    min-height: 48px;
    padding: 14px var(--spacing-lg);
  }
  
  .button-text {
    min-height: 44px;
    padding: 12px var(--spacing-md);
  }
  
  .input-license-key,
  .input-text {
    min-height: 48px;
    padding: 14px 16px;
  }
  
  .license-key-clear {
    min-width: 44px;
    min-height: 44px;
    padding: 12px;
  }
}

@media (pointer: fine) {
  /* 精确指针（鼠标）：标准交互目标 */
  .button-primary,
  .button-secondary {
    min-height: 32px;
  }
  
  .input-license-key,
  .input-text {
    min-height: 36px;
  }
}
