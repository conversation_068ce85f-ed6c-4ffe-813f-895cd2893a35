/* 
 * 主样式文件 - License激活页面
 * 页面特定样式和交互效果
 */

/* 页面入场动画 */
.license-activation-page {
  animation: page-fade-in var(--duration-normal) var(--easing-ease-out);
}

@keyframes page-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 分层入场动画 */
.activation-header {
  animation: slide-in-from-top var(--duration-normal) var(--easing-ease-out) 0ms;
}

.activation-form {
  animation: slide-in-from-bottom var(--duration-normal) var(--easing-ease-out) 100ms;
}

.activation-status {
  animation: slide-in-from-bottom var(--duration-normal) var(--easing-ease-out) 200ms;
}

.activation-actions {
  animation: slide-in-from-bottom var(--duration-normal) var(--easing-ease-out) 300ms;
}

.activation-footer {
  animation: slide-in-from-bottom var(--duration-normal) var(--easing-ease-out) 400ms;
}

@keyframes slide-in-from-top {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* License Key输入框特效 */
.license-key-input-wrapper {
  position: relative;
}

.license-key-input-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--border-radius-medium);
  background: linear-gradient(45deg, transparent, var(--primary-color), transparent);
  opacity: 0;
  z-index: -1;
  transition: opacity var(--duration-fast) var(--easing-ease-out);
}

.license-key-input-wrapper:focus-within::before {
  opacity: 0.1;
}

/* 输入验证状态动画 */
.input-license-key.validating {
  border-color: var(--info-color);
  position: relative;
}

.input-license-key.validating::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--border-radius-medium);
  background: linear-gradient(90deg, transparent, var(--info-color), transparent);
  opacity: 0.3;
  animation: validation-sweep 1s ease-in-out infinite;
}

@keyframes validation-sweep {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 成功验证动画 */
.input-license-key.success {
  animation: success-glow var(--duration-normal) var(--easing-ease-out);
}

@keyframes success-glow {
  0% { box-shadow: 0 0 0 0 rgba(16, 124, 16, 0.5); }
  50% { box-shadow: 0 0 0 4px rgba(16, 124, 16, 0.2); }
  100% { box-shadow: 0 0 0 0 rgba(16, 124, 16, 0); }
}

/* 错误验证动画 */
.input-license-key.error {
  animation: error-shake 0.4s ease-in-out;
}

@keyframes error-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

/* 按钮加载状态 */
.button-primary.loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.button-primary.loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: button-spin 1s linear infinite;
}

@keyframes button-spin {
  to { transform: rotate(360deg); }
}

/* 按钮成功状态 */
.button-primary.success {
  background-color: var(--success-color);
  border-color: var(--success-color);
  animation: button-success var(--duration-normal) var(--easing-bounce);
}

@keyframes button-success {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 按钮错误状态 */
.button-primary.error {
  background-color: var(--error-color);
  border-color: var(--error-color);
  animation: button-error 0.4s ease-in-out;
}

@keyframes button-error {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* 状态转换动画 */
.activation-status.transitioning {
  transform: scale(0.95);
  opacity: 0.8;
  transition: all var(--duration-fast) var(--easing-ease-out);
}

/* 庆祝粒子效果容器 */
.celebration-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: var(--success-color);
  border-radius: 50%;
  opacity: 0.8;
  animation: particle-float 1s ease-out forwards;
}

@keyframes particle-float {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 0.8;
  }
  100% {
    transform: translate(var(--particle-x, 0), var(--particle-y, 0)) scale(0);
    opacity: 0;
  }
}

/* 工具提示样式 */
.tooltip {
  position: absolute;
  background-color: var(--text-primary);
  color: var(--background-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-medium);
  font-size: var(--font-size-body-small);
  white-space: nowrap;
  z-index: var(--z-index-tooltip);
  opacity: 0;
  transform: translateY(4px);
  transition: all var(--duration-fast) var(--easing-ease-out);
  pointer-events: none;
}

.tooltip.show {
  opacity: 1;
  transform: translateY(0);
}

.tooltip::before {
  content: '';
  position: absolute;
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid var(--text-primary);
}

/* 通知消息样式 */
.notification {
  background-color: var(--background-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-md);
  box-shadow: var(--elevation-3);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  max-width: 400px;
  opacity: 0;
  transform: translateX(100%);
  transition: all var(--duration-normal) var(--easing-ease-out);
  pointer-events: auto;
}

.notification.show {
  opacity: 1;
  transform: translateX(0);
}

.notification.success {
  border-color: var(--success-border);
  background-color: var(--success-background);
}

.notification.error {
  border-color: var(--error-border);
  background-color: var(--error-background);
}

.notification.warning {
  border-color: var(--warning-border);
  background-color: var(--warning-background);
}

.notification.info {
  border-color: var(--info-border);
  background-color: var(--info-background);
}

.notification-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: var(--font-weight-h4);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.notification-message {
  font-size: var(--font-size-body-small);
  color: var(--text-secondary);
  line-height: var(--line-height-body-small);
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-small);
  transition: all var(--duration-fast) var(--easing-ease-out);
}

.notification-close:hover {
  color: var(--text-secondary);
  background-color: var(--background-tertiary);
}

/* 拖拽状态样式 */
.license-activation-page.drag-over {
  background-color: rgba(0, 120, 212, 0.05);
}

.license-activation-page.drag-over .activation-container {
  border: 2px dashed var(--primary-color);
  transform: scale(0.98);
}

/* 键盘导航增强 */
.keyboard-navigation *:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
  border-radius: var(--border-radius-small);
}

/* 触控反馈 */
@media (pointer: coarse) {
  .button-primary:active,
  .button-secondary:active {
    transform: scale(0.98);
  }
  
  .input-license-key:active,
  .input-text:active {
    transform: scale(0.995);
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .license-activation-page,
  .activation-header,
  .activation-form,
  .activation-status,
  .activation-actions,
  .activation-footer {
    animation: none;
  }
  
  .input-license-key.validating::after {
    animation: none;
  }
  
  .button-primary.loading::before {
    animation: none;
    border: 2px solid currentColor;
    border-radius: 0;
  }
}

/* 高对比度模式增强 */
@media (prefers-contrast: high) {
  .activation-container {
    border: 2px solid var(--text-primary);
  }
  
  .activation-status {
    border: 2px solid var(--text-primary);
  }
  
  .notification {
    border: 2px solid var(--text-primary);
  }
}

/* 暗色主题特定样式 */
[data-theme="dark"] .license-key-input-wrapper::before {
  background: linear-gradient(45deg, transparent, var(--primary-color), transparent);
}

[data-theme="dark"] .drop-overlay {
  background-color: rgba(96, 205, 255, 0.1);
}

/* 性能优化 */
.activation-container,
.button-primary,
.button-secondary,
.input-license-key,
.input-text {
  will-change: transform, opacity;
}

/* GPU加速 */
.status-icon,
.progress-ring,
.notification {
  transform: translateZ(0);
}
