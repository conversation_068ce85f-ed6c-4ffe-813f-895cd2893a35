# License激活页面数据绑定策略设计

## 📋 数据绑定架构概述

License激活页面采用严格的MVVM模式，确保View、ViewModel和Model之间的清晰分离，实现高效的数据绑定和状态管理。

## 🔗 数据绑定映射关系

### 1. 输入控件绑定

```xml
<!-- License Key输入框 -->
<TextBox Text="{Binding LicenseKey, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
         IsEnabled="{Binding IsActivating, Converter={StaticResource InverseBooleanConverter}}"
         Style="{StaticResource FluentTextBoxStyle}">
    <TextBox.InputBindings>
        <KeyBinding Key="Enter" Command="{Binding ActivateLicenseCommand}" />
    </TextBox.InputBindings>
</TextBox>

<!-- 公司名称输入框 -->
<TextBox Text="{Binding CompanyName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
         IsEnabled="{Binding IsActivating, Converter={StaticResource InverseBooleanConverter}}"
         Style="{StaticResource FluentTextBoxStyle}" />

<!-- 联系邮箱输入框 -->
<TextBox Text="{Binding ContactEmail, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
         IsEnabled="{Binding IsActivating, Converter={StaticResource InverseBooleanConverter}}"
         Style="{StaticResource FluentTextBoxStyle}" />
```

### 2. 状态显示绑定

```xml
<!-- 激活状态消息 -->
<TextBlock Text="{Binding ActivationMessage}"
           Foreground="{Binding CurrentStatus, Converter={StaticResource StatusToColorConverter}}"
           Style="{StaticResource FluentStatusTextStyle}" />

<!-- 进度指示器 -->
<ProgressBar IsIndeterminate="{Binding IsActivating}"
             Visibility="{Binding IsActivating, Converter={StaticResource BooleanToVisibilityConverter}}"
             Style="{StaticResource FluentProgressBarStyle}" />

<!-- 成功图标 -->
<Path Data="{StaticResource CheckmarkIcon}"
      Fill="{StaticResource SuccessColor}"
      Visibility="{Binding IsActivated, Converter={StaticResource BooleanToVisibilityConverter}}" />
```

### 3. 命令绑定

```xml
<!-- 激活按钮 -->
<Button Content="激活License"
        Command="{Binding ActivateLicenseCommand}"
        IsEnabled="{Binding CanActivate}"
        Style="{StaticResource FluentPrimaryButtonStyle}" />

<!-- 取消按钮 -->
<Button Content="取消"
        Command="{Binding CancelActivationCommand}"
        IsEnabled="{Binding CanCancel}"
        Style="{StaticResource FluentSecondaryButtonStyle}" />

<!-- 清除按钮 -->
<Button Content="清除"
        Command="{Binding ClearInputCommand}"
        Style="{StaticResource FluentTextButtonStyle}" />

<!-- 帮助按钮 -->
<Button Content="帮助"
        Command="{Binding GetHelpCommand}"
        Style="{StaticResource FluentIconButtonStyle}" />
```

### 4. 验证错误绑定

```xml
<!-- License Key错误提示 -->
<TextBlock Text="{Binding LicenseKeyError}"
           Foreground="{StaticResource ErrorColor}"
           Visibility="{Binding LicenseKeyError, Converter={StaticResource StringToVisibilityConverter}}"
           Style="{StaticResource FluentErrorTextStyle}" />

<!-- 邮箱错误提示 -->
<TextBlock Text="{Binding EmailError}"
           Foreground="{StaticResource ErrorColor}"
           Visibility="{Binding EmailError, Converter={StaticResource StringToVisibilityConverter}}"
           Style="{StaticResource FluentErrorTextStyle}" />
```

## 🔄 数据绑定更新策略

### 1. 属性变更通知机制

```csharp
// ViewModel中的属性实现
public string LicenseKey
{
    get => _licenseKey;
    set
    {
        if (SetProperty(ref _licenseKey, value))
        {
            // 立即验证License Key格式
            ValidateLicenseKey();
            
            // 更新命令可执行状态
            OnPropertyChanged(nameof(CanActivate));
            
            // 格式化显示（添加分隔符）
            FormatLicenseKeyDisplay();
        }
    }
}
```

### 2. 实时验证策略

```csharp
// 实时验证实现
private void ValidateLicenseKey()
{
    // 清除之前的错误
    LicenseKeyError = string.Empty;
    
    // 异步验证（避免阻塞UI）
    Task.Run(async () =>
    {
        var validationResult = await _licenseService.ValidateLicenseKeyFormat(LicenseKey);
        
        // 回到UI线程更新错误信息
        Application.Current.Dispatcher.Invoke(() =>
        {
            if (!validationResult.IsValid)
            {
                LicenseKeyError = validationResult.GetAllErrorsAsString();
            }
        });
    });
}
```

### 3. 命令状态管理

```csharp
// 命令可执行状态的动态计算
public bool CanActivate => 
    !IsActivating &&                           // 不在激活过程中
    !string.IsNullOrEmpty(LicenseKey) &&       // License Key不为空
    string.IsNullOrEmpty(LicenseKeyError) &&   // License Key无验证错误
    (string.IsNullOrEmpty(ContactEmail) ||     // 邮箱为空或
     string.IsNullOrEmpty(EmailError));        // 邮箱无验证错误
```

## 🎨 UI状态绑定策略

### 1. 多状态UI切换

```xml
<!-- 使用DataTrigger实现状态切换 -->
<Grid>
    <Grid.Style>
        <Style TargetType="Grid">
            <Style.Triggers>
                <!-- 未激活状态 -->
                <DataTrigger Binding="{Binding CurrentStatus}" Value="NotActivated">
                    <Setter Property="Background" Value="{StaticResource DefaultBackground}" />
                </DataTrigger>
                
                <!-- 激活中状态 -->
                <DataTrigger Binding="{Binding CurrentStatus}" Value="Activating">
                    <Setter Property="Background" Value="{StaticResource ProcessingBackground}" />
                </DataTrigger>
                
                <!-- 激活成功状态 -->
                <DataTrigger Binding="{Binding CurrentStatus}" Value="Activated">
                    <Setter Property="Background" Value="{StaticResource SuccessBackground}" />
                </DataTrigger>
                
                <!-- 激活失败状态 -->
                <DataTrigger Binding="{Binding CurrentStatus}" Value="Failed">
                    <Setter Property="Background" Value="{StaticResource ErrorBackground}" />
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Grid.Style>
</Grid>
```

### 2. 动画绑定

```xml
<!-- 状态变化动画 -->
<Grid>
    <Grid.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard>
                <Storyboard>
                    <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                   From="0" To="1" Duration="0:0:0.3" />
                </Storyboard>
            </BeginStoryboard>
        </EventTrigger>
    </Grid.Triggers>
</Grid>
```

## 🔧 值转换器设计

### 1. 状态到颜色转换器

```csharp
public class StatusToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is ActivationStatus status)
        {
            return status switch
            {
                ActivationStatus.NotActivated => Brushes.Gray,
                ActivationStatus.Activating => Brushes.Blue,
                ActivationStatus.Activated => Brushes.Green,
                ActivationStatus.Failed => Brushes.Red,
                ActivationStatus.Cancelled => Brushes.Orange,
                _ => Brushes.Black
            };
        }
        return Brushes.Black;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
```

### 2. License Key格式化转换器

```csharp
public class LicenseKeyFormatConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string licenseKey && !string.IsNullOrEmpty(licenseKey))
        {
            // 移除现有分隔符
            var cleanKey = licenseKey.Replace("-", "");
            
            // 添加分隔符：XXXX-XXXX-XXXX-XXXX-XXXX
            if (cleanKey.Length > 0)
            {
                var formatted = string.Empty;
                for (int i = 0; i < cleanKey.Length; i += 4)
                {
                    if (i > 0) formatted += "-";
                    formatted += cleanKey.Substring(i, Math.Min(4, cleanKey.Length - i));
                }
                return formatted;
            }
        }
        return value;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string formatted)
        {
            return formatted.Replace("-", "");
        }
        return value;
    }
}
```

## 📱 响应式数据绑定

### 1. DPI感知绑定

```xml
<!-- 根据DPI调整字体大小 -->
<TextBlock Text="{Binding ActivationMessage}"
           FontSize="{Binding Source={x:Static SystemParameters.PrimaryScreenHeight}, 
                              Converter={StaticResource DpiToFontSizeConverter}}" />
```

### 2. 窗口大小适配绑定

```xml
<!-- 根据窗口大小调整布局 -->
<Grid>
    <Grid.ColumnDefinitions>
        <ColumnDefinition Width="{Binding ActualWidth, 
                                          RelativeSource={RelativeSource AncestorType=Window},
                                          Converter={StaticResource WidthToColumnWidthConverter}}" />
        <ColumnDefinition Width="*" />
    </Grid.ColumnDefinitions>
</Grid>
```

## 🔒 数据安全绑定策略

### 1. 敏感数据保护

```csharp
// License Key的安全处理
private string _licenseKey = string.Empty;
public string LicenseKey
{
    get => _licenseKey;
    set
    {
        // 输入时自动转换为大写
        var upperValue = value?.ToUpper() ?? string.Empty;
        
        // 限制长度和字符
        var cleanValue = Regex.Replace(upperValue, @"[^A-Z0-9\-]", "");
        
        if (SetProperty(ref _licenseKey, cleanValue))
        {
            ValidateLicenseKey();
            OnPropertyChanged(nameof(CanActivate));
        }
    }
}
```

### 2. 输入防护

```xml
<!-- 防止恶意输入 -->
<TextBox Text="{Binding LicenseKey, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
         MaxLength="24"
         CharacterCasing="Upper"
         PreviewTextInput="OnLicenseKeyPreviewTextInput" />
```

## 🎯 性能优化策略

### 1. 延迟绑定

```csharp
// 使用防抖动机制避免频繁验证
private Timer _validationTimer;

private void ValidateLicenseKeyWithDelay()
{
    _validationTimer?.Stop();
    _validationTimer = new Timer(500); // 500ms延迟
    _validationTimer.Elapsed += (s, e) =>
    {
        _validationTimer.Stop();
        ValidateLicenseKey();
    };
    _validationTimer.Start();
}
```

### 2. 虚拟化绑定

```xml
<!-- 对于大量数据使用虚拟化 -->
<ListBox ItemsSource="{Binding LicenseHistory}"
         VirtualizingPanel.IsVirtualizing="True"
         VirtualizingPanel.VirtualizationMode="Recycling" />
```

这个数据绑定策略设计确保了License激活页面的高效、安全和用户友好的数据交互体验。
