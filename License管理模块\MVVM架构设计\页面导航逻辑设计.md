# License激活页面导航逻辑设计

## 📋 导航架构概述

License激活页面作为软件的入口页面，需要设计完善的导航逻辑，确保用户能够顺畅地完成激活流程并进入主应用程序。

## 🗺️ 页面导航流程图

```mermaid
graph TD
    A[应用启动] --> B{检查License状态}
    B -->|未激活| C[License激活页面]
    B -->|已激活但过期| D[License续期页面]
    B -->|已激活且有效| E[主应用程序]
    
    C --> F{激活操作}
    F -->|激活成功| G[显示成功消息]
    F -->|激活失败| H[显示错误信息]
    F -->|取消激活| I[退出应用]
    
    G --> J[延迟3秒]
    J --> E
    
    H --> K{用户选择}
    K -->|重试| C
    K -->|联系支持| L[技术支持页面]
    K -->|退出| I
    
    L --> C
    
    D --> M{续期操作}
    M -->|续期成功| E
    M -->|续期失败| N[返回激活页面]
    M -->|取消续期| I
    
    N --> C
```

## 🏗️ 导航服务接口设计

```csharp
/// <summary>
/// 导航服务接口
/// </summary>
public interface INavigationService
{
    /// <summary>
    /// 导航到指定页面
    /// </summary>
    /// <param name="pageType">页面类型</param>
    /// <param name="parameter">导航参数</param>
    /// <returns>导航结果</returns>
    Task<NavigationResult> NavigateToAsync(Type pageType, object? parameter = null);
    
    /// <summary>
    /// 导航到指定页面（泛型版本）
    /// </summary>
    /// <typeparam name="T">页面类型</typeparam>
    /// <param name="parameter">导航参数</param>
    /// <returns>导航结果</returns>
    Task<NavigationResult> NavigateToAsync<T>(object? parameter = null) where T : class;
    
    /// <summary>
    /// 返回上一页
    /// </summary>
    /// <returns>导航结果</returns>
    Task<NavigationResult> GoBackAsync();
    
    /// <summary>
    /// 关闭当前页面
    /// </summary>
    /// <param name="result">页面结果</param>
    /// <returns>导航结果</returns>
    Task<NavigationResult> CloseAsync(object? result = null);
    
    /// <summary>
    /// 退出应用程序
    /// </summary>
    /// <param name="exitCode">退出代码</param>
    void ExitApplication(int exitCode = 0);
    
    /// <summary>
    /// 检查是否可以返回
    /// </summary>
    bool CanGoBack { get; }
    
    /// <summary>
    /// 当前页面类型
    /// </summary>
    Type? CurrentPageType { get; }
    
    /// <summary>
    /// 导航历史
    /// </summary>
    IReadOnlyList<NavigationEntry> NavigationHistory { get; }
}
```

## 🎯 License激活页面导航逻辑

### 1. 页面初始化导航

```csharp
public class LicenseActivationViewModel : INotifyPropertyChanged
{
    private readonly INavigationService _navigationService;
    
    public LicenseActivationViewModel(
        ILicenseService licenseService,
        INavigationService navigationService,
        INotificationService notificationService)
    {
        _licenseService = licenseService;
        _navigationService = navigationService;
        _notificationService = notificationService;
        
        InitializeNavigationCommands();
    }
    
    private void InitializeNavigationCommands()
    {
        // 激活成功后的导航命令
        NavigateToMainAppCommand = new AsyncRelayCommand(NavigateToMainApp);
        
        // 退出应用命令
        ExitApplicationCommand = new RelayCommand(ExitApplication);
        
        // 联系技术支持命令
        ContactSupportCommand = new AsyncRelayCommand(NavigateToSupport);
        
        // 查看帮助命令
        ViewHelpCommand = new AsyncRelayCommand(NavigateToHelp);
    }
}
```

### 2. 激活成功导航

```csharp
/// <summary>
/// 激活成功后导航到主应用程序
/// </summary>
private async Task NavigateToMainApp()
{
    try
    {
        // 显示成功消息
        await _notificationService.ShowSuccessAsync(
            "激活成功", 
            "License激活成功！即将进入主应用程序...");
        
        // 延迟3秒让用户看到成功消息
        await Task.Delay(3000);
        
        // 导航到主应用程序
        var navigationResult = await _navigationService.NavigateToAsync<MainApplicationView>(
            new MainAppNavigationParameter
            {
                LicenseInfo = await _licenseService.GetCurrentLicenseAsync(),
                IsFirstTimeActivation = true
            });
        
        if (!navigationResult.IsSuccess)
        {
            await _notificationService.ShowErrorAsync(
                "导航失败", 
                "无法进入主应用程序，请重启软件");
        }
    }
    catch (Exception ex)
    {
        await _notificationService.ShowErrorAsync(
            "导航异常", 
            $"进入主应用程序时发生错误：{ex.Message}");
    }
}
```

### 3. 激活失败导航

```csharp
/// <summary>
/// 激活失败后的导航选择
/// </summary>
private async Task HandleActivationFailure(ActivationResponse response)
{
    var options = new List<DialogOption>
    {
        new("重试", "retry", true),
        new("联系技术支持", "support", false),
        new("退出程序", "exit", false)
    };
    
    var choice = await _notificationService.ShowOptionsAsync(
        "激活失败",
        $"License激活失败：{response.ErrorMessage}\n\n请选择下一步操作：",
        options);
    
    switch (choice)
    {
        case "retry":
            // 清除输入，重新激活
            ExecuteClearInput();
            break;
            
        case "support":
            await NavigateToSupport();
            break;
            
        case "exit":
            ExitApplication();
            break;
    }
}
```

### 4. 技术支持页面导航

```csharp
/// <summary>
/// 导航到技术支持页面
/// </summary>
private async Task NavigateToSupport()
{
    var supportParameter = new SupportNavigationParameter
    {
        IssueType = "License激活问题",
        LicenseKey = LicenseKey,
        ErrorMessage = ActivationMessage,
        ContactEmail = ContactEmail,
        ReturnPage = typeof(LicenseActivationView)
    };
    
    await _navigationService.NavigateToAsync<TechnicalSupportView>(supportParameter);
}
```

### 5. 帮助页面导航

```csharp
/// <summary>
/// 导航到帮助页面
/// </summary>
private async Task NavigateToHelp()
{
    var helpParameter = new HelpNavigationParameter
    {
        Topic = "License激活指南",
        Section = "激活流程",
        ReturnPage = typeof(LicenseActivationView)
    };
    
    await _navigationService.NavigateToAsync<HelpView>(helpParameter);
}
```

## 🔄 导航状态管理

### 1. 导航状态枚举

```csharp
/// <summary>
/// 导航状态枚举
/// </summary>
public enum NavigationState
{
    Idle,           // 空闲状态
    Navigating,     // 导航中
    Completed,      // 导航完成
    Failed,         // 导航失败
    Cancelled       // 导航取消
}
```

### 2. 导航状态属性

```csharp
private NavigationState _navigationState = NavigationState.Idle;
public NavigationState NavigationState
{
    get => _navigationState;
    set
    {
        if (SetProperty(ref _navigationState, value))
        {
            OnPropertyChanged(nameof(IsNavigating));
            OnPropertyChanged(nameof(CanNavigate));
        }
    }
}

public bool IsNavigating => NavigationState == NavigationState.Navigating;
public bool CanNavigate => NavigationState == NavigationState.Idle;
```

## 🎨 导航UI反馈

### 1. 导航进度指示

```xml
<!-- 导航进度指示器 -->
<ProgressBar IsIndeterminate="{Binding IsNavigating}"
             Visibility="{Binding IsNavigating, Converter={StaticResource BooleanToVisibilityConverter}}"
             Style="{StaticResource FluentProgressBarStyle}" />

<!-- 导航状态文本 -->
<TextBlock Text="{Binding NavigationStatusMessage}"
           Visibility="{Binding IsNavigating, Converter={StaticResource BooleanToVisibilityConverter}}"
           Style="{StaticResource FluentStatusTextStyle}" />
```

### 2. 导航按钮状态

```xml
<!-- 主要操作按钮 -->
<Button Content="进入主程序"
        Command="{Binding NavigateToMainAppCommand}"
        IsEnabled="{Binding CanNavigate}"
        Visibility="{Binding IsActivated, Converter={StaticResource BooleanToVisibilityConverter}}"
        Style="{StaticResource FluentPrimaryButtonStyle}" />

<!-- 退出按钮 -->
<Button Content="退出程序"
        Command="{Binding ExitApplicationCommand}"
        IsEnabled="{Binding CanNavigate}"
        Style="{StaticResource FluentSecondaryButtonStyle}" />
```

## 🔧 导航参数传递

### 1. 导航参数基类

```csharp
/// <summary>
/// 导航参数基类
/// </summary>
public abstract class NavigationParameterBase
{
    /// <summary>
    /// 参数ID
    /// </summary>
    public string ParameterId { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 来源页面类型
    /// </summary>
    public Type? SourcePageType { get; set; }
    
    /// <summary>
    /// 返回页面类型
    /// </summary>
    public Type? ReturnPageType { get; set; }
}
```

### 2. 主应用导航参数

```csharp
/// <summary>
/// 主应用程序导航参数
/// </summary>
public class MainAppNavigationParameter : NavigationParameterBase
{
    /// <summary>
    /// License信息
    /// </summary>
    public LicenseInfo? LicenseInfo { get; set; }
    
    /// <summary>
    /// 是否首次激活
    /// </summary>
    public bool IsFirstTimeActivation { get; set; }
    
    /// <summary>
    /// 启动模块
    /// </summary>
    public string? StartupModule { get; set; }
    
    /// <summary>
    /// 用户权限级别
    /// </summary>
    public PermissionLevel PermissionLevel { get; set; }
}
```

## 🛡️ 导航安全控制

### 1. 导航权限验证

```csharp
/// <summary>
/// 验证导航权限
/// </summary>
private async Task<bool> ValidateNavigationPermission(Type targetPageType)
{
    // 检查License状态
    var licenseStatus = await _licenseService.GetLicenseStatusAsync();
    if (!licenseStatus.IsActivated)
    {
        await _notificationService.ShowWarningAsync(
            "权限不足", 
            "请先激活License才能访问该功能");
        return false;
    }
    
    // 检查页面访问权限
    var requiredPermission = GetRequiredPermission(targetPageType);
    if (!await _licenseService.HasPermissionAsync(requiredPermission))
    {
        await _notificationService.ShowWarningAsync(
            "权限不足", 
            "您的License不包含访问该功能的权限");
        return false;
    }
    
    return true;
}
```

### 2. 导航历史记录

```csharp
/// <summary>
/// 导航历史记录
/// </summary>
public class NavigationEntry
{
    public Type PageType { get; set; }
    public object? Parameter { get; set; }
    public DateTime NavigationTime { get; set; }
    public string? Title { get; set; }
}
```

这个导航逻辑设计确保了License激活页面与其他页面之间的流畅导航，同时提供了完善的错误处理和用户反馈机制。
