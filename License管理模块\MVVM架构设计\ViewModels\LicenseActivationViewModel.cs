using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Text.RegularExpressions;

namespace AirConditioningMonitor.License.ViewModels
{
    /// <summary>
    /// License激活页面视图模型
    /// 负责License激活的数据绑定、命令处理和状态管理
    /// </summary>
    public class LicenseActivationViewModel : INotifyPropertyChanged
    {
        #region 私有字段
        private readonly ILicenseService _licenseService;
        private readonly INotificationService _notificationService;
        
        private string _licenseKey = string.Empty;
        private string _companyName = string.Empty;
        private string _contactEmail = string.Empty;
        private bool _isActivating = false;
        private bool _isActivated = false;
        private string _activationMessage = string.Empty;
        private ActivationStatus _currentStatus = ActivationStatus.NotActivated;
        private string _licenseKeyError = string.Empty;
        private string _emailError = string.Empty;
        #endregion

        #region 构造函数
        public LicenseActivationViewModel(
            ILicenseService licenseService, 
            INotificationService notificationService)
        {
            _licenseService = licenseService ?? throw new ArgumentNullException(nameof(licenseService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            
            InitializeCommands();
            InitializeValidation();
        }
        #endregion

        #region 数据绑定属性

        /// <summary>
        /// License Key输入
        /// 格式：XXXX-XXXX-XXXX-XXXX-XXXX (20位字符，4位一组)
        /// </summary>
        public string LicenseKey
        {
            get => _licenseKey;
            set
            {
                if (SetProperty(ref _licenseKey, value))
                {
                    ValidateLicenseKey();
                    OnPropertyChanged(nameof(CanActivate));
                }
            }
        }

        /// <summary>
        /// 公司名称（可选）
        /// </summary>
        public string CompanyName
        {
            get => _companyName;
            set => SetProperty(ref _companyName, value);
        }

        /// <summary>
        /// 联系邮箱（用于技术支持）
        /// </summary>
        public string ContactEmail
        {
            get => _contactEmail;
            set
            {
                if (SetProperty(ref _contactEmail, value))
                {
                    ValidateEmail();
                    OnPropertyChanged(nameof(CanActivate));
                }
            }
        }

        /// <summary>
        /// 是否正在激活中
        /// </summary>
        public bool IsActivating
        {
            get => _isActivating;
            set
            {
                if (SetProperty(ref _isActivating, value))
                {
                    OnPropertyChanged(nameof(CanActivate));
                    OnPropertyChanged(nameof(CanCancel));
                }
            }
        }

        /// <summary>
        /// 是否已激活成功
        /// </summary>
        public bool IsActivated
        {
            get => _isActivated;
            set => SetProperty(ref _isActivated, value);
        }

        /// <summary>
        /// 激活状态消息
        /// </summary>
        public string ActivationMessage
        {
            get => _activationMessage;
            set => SetProperty(ref _activationMessage, value);
        }

        /// <summary>
        /// 当前激活状态
        /// </summary>
        public ActivationStatus CurrentStatus
        {
            get => _currentStatus;
            set
            {
                if (SetProperty(ref _currentStatus, value))
                {
                    UpdateStatusMessage();
                }
            }
        }

        /// <summary>
        /// License Key验证错误信息
        /// </summary>
        public string LicenseKeyError
        {
            get => _licenseKeyError;
            set => SetProperty(ref _licenseKeyError, value);
        }

        /// <summary>
        /// 邮箱验证错误信息
        /// </summary>
        public string EmailError
        {
            get => _emailError;
            set => SetProperty(ref _emailError, value);
        }

        /// <summary>
        /// 是否可以执行激活操作
        /// </summary>
        public bool CanActivate => !IsActivating && 
                                   !string.IsNullOrEmpty(LicenseKey) && 
                                   string.IsNullOrEmpty(LicenseKeyError) &&
                                   (string.IsNullOrEmpty(ContactEmail) || string.IsNullOrEmpty(EmailError));

        /// <summary>
        /// 是否可以取消操作
        /// </summary>
        public bool CanCancel => IsActivating;

        #endregion

        #region 命令定义

        /// <summary>
        /// 激活License命令
        /// </summary>
        public ICommand ActivateLicenseCommand { get; private set; }

        /// <summary>
        /// 取消激活命令
        /// </summary>
        public ICommand CancelActivationCommand { get; private set; }

        /// <summary>
        /// 清除输入命令
        /// </summary>
        public ICommand ClearInputCommand { get; private set; }

        /// <summary>
        /// 获取帮助命令
        /// </summary>
        public ICommand GetHelpCommand { get; private set; }

        /// <summary>
        /// 联系技术支持命令
        /// </summary>
        public ICommand ContactSupportCommand { get; private set; }

        #endregion

        #region 命令实现

        private void InitializeCommands()
        {
            ActivateLicenseCommand = new RelayCommand(async () => await ExecuteActivateLicense(), () => CanActivate);
            CancelActivationCommand = new RelayCommand(ExecuteCancelActivation, () => CanCancel);
            ClearInputCommand = new RelayCommand(ExecuteClearInput);
            GetHelpCommand = new RelayCommand(ExecuteGetHelp);
            ContactSupportCommand = new RelayCommand(ExecuteContactSupport);
        }

        /// <summary>
        /// 执行License激活
        /// </summary>
        private async Task ExecuteActivateLicense()
        {
            try
            {
                IsActivating = true;
                CurrentStatus = ActivationStatus.Activating;

                // 创建激活请求
                var activationRequest = new ActivationRequest
                {
                    LicenseKey = LicenseKey.Replace("-", ""), // 移除分隔符
                    CompanyName = CompanyName,
                    ContactEmail = ContactEmail,
                    MachineId = Environment.MachineName,
                    ActivationTime = DateTime.Now
                };

                // 调用License服务进行激活
                var result = await _licenseService.ActivateLicenseAsync(activationRequest);

                if (result.IsSuccess)
                {
                    CurrentStatus = ActivationStatus.Activated;
                    IsActivated = true;
                    
                    // 显示成功消息
                    await _notificationService.ShowSuccessAsync(
                        "License激活成功", 
                        $"欢迎使用商用空调监控调试软件！\n权限级别：{result.PermissionLevel}\n有效期至：{result.ExpiryDate:yyyy-MM-dd}");
                }
                else
                {
                    CurrentStatus = ActivationStatus.Failed;
                    
                    // 显示错误消息
                    await _notificationService.ShowErrorAsync(
                        "License激活失败", 
                        result.ErrorMessage ?? "未知错误，请联系技术支持");
                }
            }
            catch (Exception ex)
            {
                CurrentStatus = ActivationStatus.Failed;
                await _notificationService.ShowErrorAsync("激活异常", $"激活过程中发生错误：{ex.Message}");
            }
            finally
            {
                IsActivating = false;
            }
        }

        /// <summary>
        /// 取消激活操作
        /// </summary>
        private void ExecuteCancelActivation()
        {
            _licenseService.CancelActivation();
            IsActivating = false;
            CurrentStatus = ActivationStatus.Cancelled;
        }

        /// <summary>
        /// 清除所有输入
        /// </summary>
        private void ExecuteClearInput()
        {
            LicenseKey = string.Empty;
            CompanyName = string.Empty;
            ContactEmail = string.Empty;
            LicenseKeyError = string.Empty;
            EmailError = string.Empty;
            CurrentStatus = ActivationStatus.NotActivated;
        }

        /// <summary>
        /// 获取帮助信息
        /// </summary>
        private void ExecuteGetHelp()
        {
            _notificationService.ShowInfo(
                "License激活帮助",
                "1. 请输入您收到的20位License Key\n" +
                "2. 格式：XXXX-XXXX-XXXX-XXXX-XXXX\n" +
                "3. 如需技术支持，请提供联系邮箱\n" +
                "4. 激活后软件将根据License类型开启相应功能");
        }

        /// <summary>
        /// 联系技术支持
        /// </summary>
        private void ExecuteContactSupport()
        {
            var supportInfo = "技术支持联系方式：\n" +
                            "邮箱：<EMAIL>\n" +
                            "电话：400-XXX-XXXX\n" +
                            "工作时间：周一至周五 9:00-18:00";
            
            _notificationService.ShowInfo("技术支持", supportInfo);
        }

        #endregion

        #region 数据验证

        private void InitializeValidation()
        {
            // 初始化验证规则
        }

        /// <summary>
        /// 验证License Key格式
        /// </summary>
        private void ValidateLicenseKey()
        {
            if (string.IsNullOrEmpty(LicenseKey))
            {
                LicenseKeyError = string.Empty;
                return;
            }

            // 移除分隔符进行验证
            var cleanKey = LicenseKey.Replace("-", "");
            
            if (cleanKey.Length != 20)
            {
                LicenseKeyError = "License Key必须为20位字符";
                return;
            }

            if (!Regex.IsMatch(cleanKey, @"^[A-Z0-9]{20}$"))
            {
                LicenseKeyError = "License Key只能包含大写字母和数字";
                return;
            }

            LicenseKeyError = string.Empty;
        }

        /// <summary>
        /// 验证邮箱格式
        /// </summary>
        private void ValidateEmail()
        {
            if (string.IsNullOrEmpty(ContactEmail))
            {
                EmailError = string.Empty;
                return;
            }

            var emailPattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
            if (!Regex.IsMatch(ContactEmail, emailPattern))
            {
                EmailError = "请输入有效的邮箱地址";
                return;
            }

            EmailError = string.Empty;
        }

        /// <summary>
        /// 更新状态消息
        /// </summary>
        private void UpdateStatusMessage()
        {
            ActivationMessage = CurrentStatus switch
            {
                ActivationStatus.NotActivated => "请输入License Key进行激活",
                ActivationStatus.Activating => "正在激活License，请稍候...",
                ActivationStatus.Activated => "License激活成功！",
                ActivationStatus.Failed => "License激活失败，请检查License Key或联系技术支持",
                ActivationStatus.Cancelled => "激活已取消",
                _ => string.Empty
            };
        }

        #endregion

        #region INotifyPropertyChanged实现

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T backingStore, T value, [CallerMemberName] string propertyName = "")
        {
            if (EqualityComparer<T>.Default.Equals(backingStore, value))
                return false;

            backingStore = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }

    /// <summary>
    /// 激活状态枚举
    /// </summary>
    public enum ActivationStatus
    {
        NotActivated,   // 未激活
        Activating,     // 激活中
        Activated,      // 已激活
        Failed,         // 激活失败
        Cancelled       // 已取消
    }
}
