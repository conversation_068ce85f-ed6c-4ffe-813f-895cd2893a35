/**
 * License激活页面 - 主应用程序
 * 基于MVVM模式的JavaScript实现
 */

import { LicenseActivationViewModel } from './data-binding.js';
import { EventHandlers } from './event-handlers.js';
import { NavigationManager } from './navigation.js';
import { ComponentManager } from './components.js';
import { AccessibilityManager } from './accessibility.js';
import { Utils } from './utils.js';

/**
 * 主应用程序类
 */
class LicenseActivationApp {
    constructor() {
        this.viewModel = null;
        this.eventHandlers = null;
        this.navigationManager = null;
        this.componentManager = null;
        this.accessibilityManager = null;
        this.isInitialized = false;
        
        // 性能监控
        this.performanceMetrics = {
            startTime: performance.now(),
            initTime: null,
            firstInteractionTime: null
        };
        
        // 错误处理
        this.setupErrorHandling();
    }

    /**
     * 初始化应用程序
     */
    async initialize() {
        try {
            console.log('🚀 初始化License激活应用程序...');
            
            // 检查浏览器兼容性
            if (!this.checkBrowserCompatibility()) {
                this.showBrowserCompatibilityError();
                return;
            }
            
            // 初始化各个管理器
            await this.initializeManagers();
            
            // 设置数据绑定
            this.setupDataBinding();
            
            // 设置事件处理
            this.setupEventHandlers();
            
            // 设置无障碍功能
            this.setupAccessibility();
            
            // 设置主题
            this.setupTheme();
            
            // 设置用户模式
            this.setupUserMode();
            
            // 播放入场动画
            this.playEntranceAnimation();
            
            // 设置性能监控
            this.setupPerformanceMonitoring();
            
            this.isInitialized = true;
            this.performanceMetrics.initTime = performance.now() - this.performanceMetrics.startTime;
            
            console.log(`✅ 应用程序初始化完成 (${this.performanceMetrics.initTime.toFixed(2)}ms)`);
            
            // 触发初始化完成事件
            this.dispatchEvent('app:initialized', {
                initTime: this.performanceMetrics.initTime
            });
            
        } catch (error) {
            console.error('❌ 应用程序初始化失败:', error);
            this.handleInitializationError(error);
        }
    }

    /**
     * 初始化各个管理器
     */
    async initializeManagers() {
        // 初始化ViewModel
        this.viewModel = new LicenseActivationViewModel();
        
        // 初始化事件处理器
        this.eventHandlers = new EventHandlers(this.viewModel);
        
        // 初始化导航管理器
        this.navigationManager = new NavigationManager();
        
        // 初始化组件管理器
        this.componentManager = new ComponentManager();
        
        // 初始化无障碍管理器
        this.accessibilityManager = new AccessibilityManager();
        
        // 等待所有管理器初始化完成
        await Promise.all([
            this.viewModel.initialize(),
            this.eventHandlers.initialize(),
            this.navigationManager.initialize(),
            this.componentManager.initialize(),
            this.accessibilityManager.initialize()
        ]);
    }

    /**
     * 设置数据绑定
     */
    setupDataBinding() {
        // 绑定输入框
        this.viewModel.bindElement('licenseKey', document.getElementById('license-key-input'));
        this.viewModel.bindElement('companyName', document.getElementById('company-name-input'));
        this.viewModel.bindElement('contactEmail', document.getElementById('contact-email-input'));
        
        // 绑定按钮
        this.viewModel.bindElement('activateButton', document.getElementById('activate-button'));
        this.viewModel.bindElement('cancelButton', document.getElementById('cancel-button'));
        
        // 绑定状态显示
        this.viewModel.bindElement('statusMessage', document.getElementById('status-message'));
        this.viewModel.bindElement('statusIcon', document.querySelector('.status-icon'));
        
        console.log('📊 数据绑定设置完成');
    }

    /**
     * 设置事件处理
     */
    setupEventHandlers() {
        // 设置表单事件
        this.eventHandlers.setupFormEvents();
        
        // 设置按钮事件
        this.eventHandlers.setupButtonEvents();
        
        // 设置键盘事件
        this.eventHandlers.setupKeyboardEvents();
        
        // 设置拖拽事件
        this.eventHandlers.setupDragDropEvents();
        
        // 设置窗口事件
        this.eventHandlers.setupWindowEvents();
        
        console.log('🎮 事件处理设置完成');
    }

    /**
     * 设置无障碍功能
     */
    setupAccessibility() {
        // 设置屏幕阅读器支持
        this.accessibilityManager.setupScreenReaderSupport();
        
        // 设置键盘导航
        this.accessibilityManager.setupKeyboardNavigation();
        
        // 设置焦点管理
        this.accessibilityManager.setupFocusManagement();
        
        // 设置高对比度模式
        this.accessibilityManager.setupHighContrastMode();
        
        console.log('♿ 无障碍功能设置完成');
    }

    /**
     * 设置主题
     */
    setupTheme() {
        const savedTheme = localStorage.getItem('license-activation-theme');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        const theme = savedTheme || (systemPrefersDark ? 'dark' : 'light');
        document.body.setAttribute('data-theme', theme);
        
        // 监听系统主题变化
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('license-activation-theme')) {
                document.body.setAttribute('data-theme', e.matches ? 'dark' : 'light');
            }
        });
        
        console.log(`🎨 主题设置完成: ${theme}`);
    }

    /**
     * 设置用户模式
     */
    setupUserMode() {
        const savedUserMode = localStorage.getItem('license-activation-user-mode');
        const userMode = savedUserMode || 'basic';
        
        document.body.setAttribute('data-user-mode', userMode);
        
        console.log(`👤 用户模式设置完成: ${userMode}`);
    }

    /**
     * 播放入场动画
     */
    playEntranceAnimation() {
        const container = document.querySelector('.activation-container');
        if (container) {
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            
            // 使用requestAnimationFrame确保动画流畅
            requestAnimationFrame(() => {
                container.style.transition = 'opacity 300ms ease-out, transform 300ms ease-out';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            });
        }
    }

    /**
     * 设置性能监控
     */
    setupPerformanceMonitoring() {
        // 监控首次交互时间
        const recordFirstInteraction = () => {
            if (!this.performanceMetrics.firstInteractionTime) {
                this.performanceMetrics.firstInteractionTime = performance.now() - this.performanceMetrics.startTime;
                console.log(`⚡ 首次交互时间: ${this.performanceMetrics.firstInteractionTime.toFixed(2)}ms`);
            }
        };
        
        // 监听各种交互事件
        ['click', 'keydown', 'touchstart', 'input'].forEach(eventType => {
            document.addEventListener(eventType, recordFirstInteraction, { once: true, passive: true });
        });
        
        // 监控内存使用（如果支持）
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (memory.usedJSHeapSize > 50 * 1024 * 1024) { // 50MB
                    console.warn('⚠️ 内存使用过高:', memory);
                }
            }, 30000); // 每30秒检查一次
        }
    }

    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility() {
        const requiredFeatures = [
            'Promise',
            'fetch',
            'localStorage',
            'addEventListener',
            'querySelector',
            'classList'
        ];
        
        return requiredFeatures.every(feature => {
            if (feature === 'Promise') return typeof Promise !== 'undefined';
            if (feature === 'fetch') return typeof fetch !== 'undefined';
            if (feature === 'localStorage') return typeof localStorage !== 'undefined';
            if (feature === 'addEventListener') return typeof document.addEventListener !== 'undefined';
            if (feature === 'querySelector') return typeof document.querySelector !== 'undefined';
            if (feature === 'classList') return 'classList' in document.createElement('div');
            return true;
        });
    }

    /**
     * 显示浏览器兼容性错误
     */
    showBrowserCompatibilityError() {
        const errorHtml = `
            <div style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: #f5f5f5;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: 'Segoe UI', sans-serif;
                z-index: 9999;
            ">
                <div style="
                    background: white;
                    padding: 40px;
                    border-radius: 8px;
                    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
                    text-align: center;
                    max-width: 400px;
                ">
                    <h2 style="color: #d13438; margin-bottom: 16px;">浏览器不兼容</h2>
                    <p style="color: #605e5c; margin-bottom: 24px;">
                        您的浏览器版本过低，无法运行此应用程序。<br>
                        请升级到最新版本的Chrome、Firefox、Safari或Edge浏览器。
                    </p>
                    <button onclick="location.reload()" style="
                        background: #0078d4;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                    ">重新加载</button>
                </div>
            </div>
        `;
        
        document.body.innerHTML = errorHtml;
    }

    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', (event) => {
            console.error('❌ 全局错误:', event.error);
            this.handleError(event.error, 'global');
        });
        
        // Promise错误处理
        window.addEventListener('unhandledrejection', (event) => {
            console.error('❌ 未处理的Promise拒绝:', event.reason);
            this.handleError(event.reason, 'promise');
        });
    }

    /**
     * 处理初始化错误
     */
    handleInitializationError(error) {
        const errorContainer = document.createElement('div');
        errorContainer.innerHTML = `
            <div class="error-container" style="
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--error-background);
                border: 1px solid var(--error-border);
                border-radius: 8px;
                padding: 24px;
                text-align: center;
                z-index: 9999;
            ">
                <h3 style="color: var(--error-color); margin-bottom: 16px;">初始化失败</h3>
                <p style="color: var(--text-primary); margin-bottom: 16px;">
                    应用程序初始化时发生错误，请刷新页面重试。
                </p>
                <button onclick="location.reload()" class="button-primary">
                    刷新页面
                </button>
            </div>
        `;
        
        document.body.appendChild(errorContainer);
    }

    /**
     * 通用错误处理
     */
    handleError(error, type = 'unknown') {
        // 记录错误
        console.error(`❌ ${type}错误:`, error);
        
        // 发送错误报告（生产环境中）
        if (process.env.NODE_ENV === 'production') {
            this.sendErrorReport(error, type);
        }
        
        // 显示用户友好的错误消息
        this.showErrorNotification('发生了一个错误，请稍后重试。');
    }

    /**
     * 显示错误通知
     */
    showErrorNotification(message) {
        if (this.componentManager && this.componentManager.notification) {
            this.componentManager.notification.show({
                type: 'error',
                title: '错误',
                message: message,
                duration: 5000
            });
        }
    }

    /**
     * 发送错误报告
     */
    sendErrorReport(error, type) {
        // 在生产环境中，这里可以发送错误报告到监控服务
        const errorData = {
            message: error.message || String(error),
            stack: error.stack || '',
            type: type,
            userAgent: navigator.userAgent,
            url: window.location.href,
            timestamp: new Date().toISOString()
        };

        // 示例：发送到错误监控服务
        // fetch('/api/errors', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(errorData)
        // }).catch(() => {}); // 忽略发送失败
    }

    /**
     * 分发自定义事件
     */
    dispatchEvent(eventName, detail = {}) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }

    /**
     * 销毁应用程序
     */
    destroy() {
        if (this.viewModel) this.viewModel.destroy();
        if (this.eventHandlers) this.eventHandlers.destroy();
        if (this.navigationManager) this.navigationManager.destroy();
        if (this.componentManager) this.componentManager.destroy();
        if (this.accessibilityManager) this.accessibilityManager.destroy();
        
        this.isInitialized = false;
        console.log('🗑️ 应用程序已销毁');
    }
}

// 创建全局应用实例
const app = new LicenseActivationApp();

// DOM加载完成后初始化应用
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => app.initialize());
} else {
    app.initialize();
}

// 导出应用实例（用于调试）
window.LicenseActivationApp = app;

export default app;
