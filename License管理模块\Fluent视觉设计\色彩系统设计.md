# License激活页面色彩系统设计

## 🎨 Fluent Design色彩体系

基于Microsoft Fluent Design System，为商用空调监控调试软件License激活页面设计专业的色彩系统。

## 🔵 主色彩定义

### 1. 品牌主色 (Primary Colors)
```css
/* 主品牌色 - 专业蓝色系 */
--primary-color: #0078D4;           /* Fluent Blue - 主要操作按钮、链接 */
--primary-hover: #106EBE;           /* 悬停状态 */
--primary-pressed: #005A9E;         /* 按下状态 */
--primary-disabled: #A6A6A6;        /* 禁用状态 */

/* 次要品牌色 - 辅助蓝色 */
--secondary-color: #40E0D0;          /* 青色 - 辅助元素、状态指示 */
--secondary-hover: #36C7B8;          /* 悬停状态 */
--secondary-pressed: #2CA89A;        /* 按下状态 */

/* 强调色 - 企业橙色 */
--accent-color: #FF8C00;             /* 橙色 - 重要提示、警告 */
--accent-hover: #E67E00;             /* 悬停状态 */
--accent-pressed: #CC7000;           /* 按下状态 */
```

### 2. 功能色彩定义
```css
/* 成功状态色 */
--success-color: #107C10;            /* 绿色 - 激活成功、验证通过 */
--success-background: #DFF6DD;       /* 成功背景色 */
--success-border: #9FD89F;           /* 成功边框色 */

/* 警告状态色 */
--warning-color: #FF8C00;            /* 橙色 - 即将过期、需要注意 */
--warning-background: #FFF4CE;       /* 警告背景色 */
--warning-border: #FFCC80;           /* 警告边框色 */

/* 错误状态色 */
--error-color: #D13438;              /* 红色 - 激活失败、验证错误 */
--error-background: #FDE7E9;         /* 错误背景色 */
--error-border: #F1AEB5;             /* 错误边框色 */

/* 信息状态色 */
--info-color: #0078D4;               /* 蓝色 - 提示信息、帮助说明 */
--info-background: #E1F5FE;          /* 信息背景色 */
--info-border: #81D4FA;              /* 信息边框色 */
```

### 3. 中性色彩定义
```css
/* 文本色彩层级 */
--text-primary: #323130;             /* 主要文本 - 标题、重要内容 */
--text-secondary: #605E5C;           /* 次要文本 - 描述、说明 */
--text-tertiary: #8A8886;            /* 三级文本 - 辅助信息 */
--text-disabled: #A19F9D;            /* 禁用文本 */
--text-placeholder: #C8C6C4;         /* 占位符文本 */

/* 背景色彩层级 */
--background-primary: #FFFFFF;        /* 主背景 - 页面主体 */
--background-secondary: #FAFAFA;      /* 次背景 - 卡片、面板 */
--background-tertiary: #F3F2F1;      /* 三级背景 - 分组区域 */
--background-quaternary: #EDEBE9;     /* 四级背景 - 输入框背景 */

/* 边框和分割线 */
--border-primary: #EDEBE9;           /* 主要边框 */
--border-secondary: #E1DFDD;         /* 次要边框 */
--border-focus: #0078D4;             /* 焦点边框 */
--divider-color: #EDEBE9;            /* 分割线颜色 */

/* 阴影色彩 */
--shadow-light: rgba(0, 0, 0, 0.1);  /* 轻阴影 */
--shadow-medium: rgba(0, 0, 0, 0.16); /* 中等阴影 */
--shadow-heavy: rgba(0, 0, 0, 0.24);  /* 重阴影 */
```

## 🌙 暗色主题适配

### 1. 暗色主题色彩定义
```css
/* 暗色主题 - 主色彩 */
[data-theme="dark"] {
  --primary-color: #60CDFF;           /* 暗色主题蓝色 */
  --primary-hover: #4FC3F7;           /* 悬停状态 */
  --primary-pressed: #29B6F6;         /* 按下状态 */
  
  /* 暗色主题 - 文本色彩 */
  --text-primary: #FFFFFF;            /* 主要文本 */
  --text-secondary: #C8C6C4;          /* 次要文本 */
  --text-tertiary: #A19F9D;           /* 三级文本 */
  --text-disabled: #605E5C;           /* 禁用文本 */
  
  /* 暗色主题 - 背景色彩 */
  --background-primary: #1F1F1F;      /* 主背景 */
  --background-secondary: #2D2D2D;    /* 次背景 */
  --background-tertiary: #404040;     /* 三级背景 */
  --background-quaternary: #4A4A4A;   /* 四级背景 */
  
  /* 暗色主题 - 边框色彩 */
  --border-primary: #484644;          /* 主要边框 */
  --border-secondary: #3B3A39;        /* 次要边框 */
  --border-focus: #60CDFF;            /* 焦点边框 */
}
```

## 🎯 License激活页面专用色彩

### 1. 激活状态色彩映射
```css
/* 激活状态专用色彩 */
--activation-idle: var(--text-secondary);      /* 未激活状态 */
--activation-processing: var(--primary-color); /* 激活中状态 */
--activation-success: var(--success-color);    /* 激活成功 */
--activation-failed: var(--error-color);       /* 激活失败 */
--activation-cancelled: var(--warning-color);  /* 激活取消 */

/* License Key输入框色彩 */
--license-input-background: var(--background-primary);
--license-input-border: var(--border-primary);
--license-input-border-focus: var(--border-focus);
--license-input-border-error: var(--error-color);
--license-input-text: var(--text-primary);
--license-input-placeholder: var(--text-placeholder);

/* 进度指示器色彩 */
--progress-background: var(--background-tertiary);
--progress-fill: var(--primary-color);
--progress-indeterminate: linear-gradient(
  90deg, 
  transparent, 
  var(--primary-color), 
  transparent
);
```

### 2. 多层次用户界面色彩
```css
/* 基础用户模式 - 简化色彩 */
.user-mode-basic {
  --interface-complexity: minimal;
  --color-count: 4; /* 限制使用的颜色数量 */
  --primary-actions: var(--primary-color);
  --secondary-actions: var(--text-secondary);
}

/* 高级用户模式 - 丰富色彩 */
.user-mode-advanced {
  --interface-complexity: rich;
  --color-count: 8;
  --primary-actions: var(--primary-color);
  --secondary-actions: var(--secondary-color);
  --tertiary-actions: var(--accent-color);
}

/* 专业技术用户模式 - 完整色彩 */
.user-mode-professional {
  --interface-complexity: complete;
  --color-count: 12;
  --primary-actions: var(--primary-color);
  --secondary-actions: var(--secondary-color);
  --tertiary-actions: var(--accent-color);
  --status-indicators: all; /* 显示所有状态指示器 */
}
```

## 🔍 色彩无障碍验证

### 1. 对比度标准
```css
/* WCAG 2.1 AA标准对比度验证 */
/* 所有文本与背景对比度 ≥ 4.5:1 */

/* 主要文本对比度 */
--contrast-primary-text: 13.1:1;     /* #323130 on #FFFFFF */
--contrast-secondary-text: 7.8:1;    /* #605E5C on #FFFFFF */
--contrast-tertiary-text: 4.6:1;     /* #8A8886 on #FFFFFF */

/* 按钮对比度 */
--contrast-primary-button: 4.5:1;    /* #FFFFFF on #0078D4 */
--contrast-secondary-button: 4.7:1;  /* #323130 on #F3F2F1 */

/* 状态色对比度 */
--contrast-success: 5.2:1;           /* #107C10 on #FFFFFF */
--contrast-warning: 4.8:1;           /* #FF8C00 on #FFFFFF */
--contrast-error: 5.1:1;             /* #D13438 on #FFFFFF */
```

### 2. 色盲友好设计
```css
/* 色盲友好色彩组合 */
--colorblind-safe-success: #107C10;  /* 绿色 - 使用形状辅助 */
--colorblind-safe-warning: #FF8C00;  /* 橙色 - 使用图标辅助 */
--colorblind-safe-error: #D13438;    /* 红色 - 使用文字辅助 */

/* 不依赖颜色的状态指示 */
.status-indicator {
  /* 使用图标 + 颜色 + 文字的组合方式 */
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator::before {
  content: '';
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--status-color);
}

.status-success::before { 
  background-color: var(--success-color);
  content: '✓'; /* 使用符号辅助 */
}

.status-error::before { 
  background-color: var(--error-color);
  content: '✗'; /* 使用符号辅助 */
}
```

## 📱 响应式色彩适配

### 1. DPI感知色彩
```css
/* 高DPI显示器色彩调整 */
@media (min-resolution: 144dpi) {
  :root {
    /* 高DPI下增强对比度 */
    --text-primary: #2B2B2B;         /* 稍微加深文本色 */
    --border-primary: #D6D4D2;       /* 稍微加深边框色 */
  }
}

/* 超高DPI显示器色彩调整 */
@media (min-resolution: 192dpi) {
  :root {
    /* 超高DPI下进一步增强对比度 */
    --text-primary: #1F1F1F;
    --border-primary: #C8C6C4;
  }
}
```

### 2. 窗口大小适配色彩
```css
/* 小窗口模式 - 简化色彩 */
@media (max-width: 768px) {
  :root {
    --color-complexity: simplified;
    /* 减少使用的颜色种类，突出主要操作 */
    --secondary-color: var(--text-secondary);
    --accent-color: var(--primary-color);
  }
}

/* 大窗口模式 - 丰富色彩 */
@media (min-width: 1200px) {
  :root {
    --color-complexity: rich;
    /* 可以使用更多颜色层次 */
    --quaternary-color: #6B69D6;     /* 紫色 - 额外的强调色 */
  }
}
```

## 🎨 色彩使用指南

### 1. 主要操作色彩
- **激活按钮**：使用主品牌色 `--primary-color`
- **取消按钮**：使用次要文本色 `--text-secondary`
- **帮助链接**：使用信息色 `--info-color`
- **技术支持**：使用强调色 `--accent-color`

### 2. 状态反馈色彩
- **激活成功**：使用成功色 `--success-color` + 成功背景
- **激活失败**：使用错误色 `--error-color` + 错误背景
- **激活中**：使用主品牌色 `--primary-color` + 动画效果
- **输入验证错误**：使用错误色边框 + 错误文本

### 3. 层次结构色彩
- **页面标题**：主要文本色 `--text-primary`
- **区块标题**：次要文本色 `--text-secondary`
- **说明文字**：三级文本色 `--text-tertiary`
- **占位符**：占位符色 `--text-placeholder`

这个色彩系统确保了License激活页面在各种使用场景下都能提供清晰、专业、无障碍的视觉体验。
