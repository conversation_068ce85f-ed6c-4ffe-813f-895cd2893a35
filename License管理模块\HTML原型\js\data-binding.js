/**
 * License激活页面 - 数据绑定模块
 * 模拟WPF的MVVM数据绑定机制
 */

import { Utils } from './utils.js';

/**
 * 可观察对象基类
 */
class ObservableObject {
    constructor() {
        this._properties = new Map();
        this._bindings = new Map();
        this._validators = new Map();
    }

    /**
     * 设置属性值
     */
    setProperty(propertyName, value) {
        const oldValue = this._properties.get(propertyName);
        
        if (oldValue !== value) {
            // 验证属性值
            const validator = this._validators.get(propertyName);
            if (validator) {
                const validationResult = validator(value);
                if (!validationResult.isValid) {
                    this.notifyValidationError(propertyName, validationResult.errors);
                    return false;
                }
            }
            
            this._properties.set(propertyName, value);
            this.notifyPropertyChanged(propertyName, value, oldValue);
            return true;
        }
        return false;
    }

    /**
     * 获取属性值
     */
    getProperty(propertyName) {
        return this._properties.get(propertyName);
    }

    /**
     * 绑定元素到属性
     */
    bindElement(propertyName, element) {
        if (!element) return;

        const bindings = this._bindings.get(propertyName) || [];
        bindings.push(element);
        this._bindings.set(propertyName, bindings);

        // 设置初始值
        this.updateElementFromProperty(propertyName, element);

        // 设置元素事件监听
        this.setupElementEventListeners(propertyName, element);
    }

    /**
     * 设置属性验证器
     */
    setValidator(propertyName, validator) {
        this._validators.set(propertyName, validator);
    }

    /**
     * 通知属性变更
     */
    notifyPropertyChanged(propertyName, newValue, oldValue) {
        const bindings = this._bindings.get(propertyName) || [];
        bindings.forEach(element => {
            this.updateElementFromProperty(propertyName, element);
        });

        // 触发属性变更事件
        this.dispatchEvent('propertyChanged', {
            propertyName,
            newValue,
            oldValue
        });
    }

    /**
     * 通知验证错误
     */
    notifyValidationError(propertyName, errors) {
        this.dispatchEvent('validationError', {
            propertyName,
            errors
        });
    }

    /**
     * 更新元素值
     */
    updateElementFromProperty(propertyName, element) {
        const value = this.getProperty(propertyName) || '';
        
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
            if (element.value !== value) {
                element.value = value;
            }
        } else if (element.tagName === 'BUTTON') {
            element.disabled = !this.getProperty('can' + Utils.capitalize(propertyName));
        } else {
            if (element.textContent !== value) {
                element.textContent = value;
            }
        }
    }

    /**
     * 设置元素事件监听
     */
    setupElementEventListeners(propertyName, element) {
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
            // 输入事件
            element.addEventListener('input', (e) => {
                this.setProperty(propertyName, e.target.value);
            });

            // 失去焦点事件
            element.addEventListener('blur', (e) => {
                this.validateProperty(propertyName, e.target.value);
            });
        }
    }

    /**
     * 验证属性
     */
    validateProperty(propertyName, value) {
        const validator = this._validators.get(propertyName);
        if (validator) {
            const result = validator(value);
            if (!result.isValid) {
                this.notifyValidationError(propertyName, result.errors);
            }
            return result;
        }
        return { isValid: true, errors: [] };
    }

    /**
     * 分发事件
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }
}

/**
 * License激活ViewModel
 */
export class LicenseActivationViewModel extends ObservableObject {
    constructor() {
        super();
        
        // 初始化属性
        this.initializeProperties();
        
        // 设置验证器
        this.setupValidators();
        
        // 设置命令
        this.setupCommands();
        
        // 监听事件
        this.setupEventListeners();
    }

    /**
     * 初始化属性
     */
    initializeProperties() {
        // 输入属性
        this.setProperty('licenseKey', '');
        this.setProperty('companyName', '');
        this.setProperty('contactEmail', '');
        
        // 状态属性
        this.setProperty('activationStatus', 'idle'); // idle, activating, success, error
        this.setProperty('activationMessage', '请输入License Key进行激活');
        this.setProperty('isActivating', false);
        this.setProperty('isActivated', false);
        
        // 验证错误属性
        this.setProperty('licenseKeyError', '');
        this.setProperty('emailError', '');
        
        // 命令可执行状态
        this.setProperty('canActivate', false);
        this.setProperty('canCancel', true);
        this.setProperty('canClear', false);
    }

    /**
     * 设置验证器
     */
    setupValidators() {
        // License Key验证器
        this.setValidator('licenseKey', (value) => {
            const errors = [];
            
            if (!value || value.trim() === '') {
                return { isValid: true, errors }; // 空值允许，但不能激活
            }
            
            // 移除分隔符
            const cleanValue = value.replace(/-/g, '');
            
            // 长度验证
            if (cleanValue.length !== 20) {
                errors.push('License Key必须为20位字符');
            }
            
            // 字符验证
            if (!/^[A-Z0-9]+$/.test(cleanValue)) {
                errors.push('License Key只能包含大写字母和数字');
            }
            
            // 格式验证（简单的校验和）
            if (cleanValue.length === 20) {
                const checksum = this.calculateLicenseKeyChecksum(cleanValue);
                if (!checksum.isValid) {
                    errors.push('License Key格式无效');
                }
            }
            
            return {
                isValid: errors.length === 0,
                errors
            };
        });

        // 邮箱验证器
        this.setValidator('contactEmail', (value) => {
            const errors = [];
            
            if (!value || value.trim() === '') {
                return { isValid: true, errors }; // 邮箱是可选的
            }
            
            // 邮箱格式验证
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!emailRegex.test(value)) {
                errors.push('请输入有效的邮箱地址');
            }
            
            // 长度验证
            if (value.length > 100) {
                errors.push('邮箱地址长度不能超过100个字符');
            }
            
            return {
                isValid: errors.length === 0,
                errors
            };
        });
    }

    /**
     * 设置命令
     */
    setupCommands() {
        this.commands = {
            activate: this.createCommand(() => this.executeLicenseActivation()),
            cancel: this.createCommand(() => this.executeCancelActivation()),
            clear: this.createCommand(() => this.executeClearInput()),
            help: this.createCommand(() => this.executeGetHelp()),
            support: this.createCommand(() => this.executeContactSupport())
        };
    }

    /**
     * 创建命令
     */
    createCommand(executeFunction, canExecuteFunction = null) {
        return {
            execute: executeFunction,
            canExecute: canExecuteFunction || (() => true)
        };
    }

    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 监听属性变更
        document.addEventListener('propertyChanged', (e) => {
            const { propertyName, newValue } = e.detail;
            
            if (propertyName === 'licenseKey') {
                this.onLicenseKeyChanged(newValue);
            } else if (propertyName === 'contactEmail') {
                this.onContactEmailChanged(newValue);
            }
        });

        // 监听验证错误
        document.addEventListener('validationError', (e) => {
            const { propertyName, errors } = e.detail;
            this.displayValidationErrors(propertyName, errors);
        });
    }

    /**
     * License Key变更处理
     */
    onLicenseKeyChanged(value) {
        // 格式化显示
        const formatted = this.formatLicenseKey(value);
        if (formatted !== value) {
            // 更新格式化后的值
            setTimeout(() => {
                const element = document.getElementById('license-key-input');
                if (element && element.value !== formatted) {
                    element.value = formatted;
                    this._properties.set('licenseKey', formatted);
                }
            }, 0);
        }
        
        // 更新命令状态
        this.updateCanActivate();
        this.updateCanClear();
        
        // 清除之前的错误
        this.setProperty('licenseKeyError', '');
    }

    /**
     * 联系邮箱变更处理
     */
    onContactEmailChanged(value) {
        // 清除之前的错误
        this.setProperty('emailError', '');
        
        // 更新命令状态
        this.updateCanActivate();
    }

    /**
     * 格式化License Key
     */
    formatLicenseKey(value) {
        if (!value) return '';
        
        // 移除现有分隔符并转换为大写
        const clean = value.replace(/-/g, '').toUpperCase();
        
        // 限制长度
        const limited = clean.substring(0, 20);
        
        // 添加分隔符
        const formatted = limited.replace(/(.{4})/g, '$1-').replace(/-$/, '');
        
        return formatted;
    }

    /**
     * 计算License Key校验和
     */
    calculateLicenseKeyChecksum(cleanKey) {
        // 简单的校验和算法（实际应用中应使用更复杂的算法）
        let sum = 0;
        for (let i = 0; i < cleanKey.length; i++) {
            const char = cleanKey.charCodeAt(i);
            sum += char * (i + 1);
        }
        
        // 简单验证：校验和应该是偶数
        return {
            isValid: sum % 2 === 0,
            checksum: sum
        };
    }

    /**
     * 更新激活命令可执行状态
     */
    updateCanActivate() {
        const licenseKey = this.getProperty('licenseKey') || '';
        const isActivating = this.getProperty('isActivating');
        const hasLicenseKeyError = this.getProperty('licenseKeyError') !== '';
        const hasEmailError = this.getProperty('emailError') !== '';
        
        const canActivate = !isActivating && 
                           licenseKey.replace(/-/g, '').length === 20 && 
                           !hasLicenseKeyError && 
                           !hasEmailError;
        
        this.setProperty('canActivate', canActivate);
    }

    /**
     * 更新清除命令可执行状态
     */
    updateCanClear() {
        const licenseKey = this.getProperty('licenseKey') || '';
        const canClear = licenseKey.length > 0;
        this.setProperty('canClear', canClear);
    }

    /**
     * 显示验证错误
     */
    displayValidationErrors(propertyName, errors) {
        const errorMessage = errors.join('; ');
        
        if (propertyName === 'licenseKey') {
            this.setProperty('licenseKeyError', errorMessage);
            this.updateElementError('license-key-error', errorMessage);
        } else if (propertyName === 'contactEmail') {
            this.setProperty('emailError', errorMessage);
            this.updateElementError('contact-email-error', errorMessage);
        }
        
        this.updateCanActivate();
    }

    /**
     * 更新元素错误显示
     */
    updateElementError(elementId, errorMessage) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = errorMessage;
            element.style.display = errorMessage ? 'block' : 'none';
        }
    }

    /**
     * 执行License激活
     */
    async executeLicenseActivation() {
        try {
            this.setProperty('isActivating', true);
            this.setProperty('activationStatus', 'activating');
            this.setProperty('activationMessage', '正在激活License...');
            
            // 模拟激活过程
            const result = await this.simulateLicenseActivation();
            
            if (result.success) {
                this.setProperty('activationStatus', 'success');
                this.setProperty('activationMessage', 'License激活成功！');
                this.setProperty('isActivated', true);
                
                // 3秒后导航到主应用
                setTimeout(() => {
                    this.navigateToMainApplication();
                }, 3000);
            } else {
                this.setProperty('activationStatus', 'error');
                this.setProperty('activationMessage', result.errorMessage);
            }
        } catch (error) {
            this.setProperty('activationStatus', 'error');
            this.setProperty('activationMessage', '激活过程中发生错误，请重试');
        } finally {
            this.setProperty('isActivating', false);
            this.updateCanActivate();
        }
    }

    /**
     * 模拟License激活
     */
    async simulateLicenseActivation() {
        // 模拟网络延迟
        await Utils.delay(2000);
        
        const licenseKey = this.getProperty('licenseKey').replace(/-/g, '');
        
        // 模拟激活逻辑
        if (licenseKey === 'ABCD1234EFGH5678IJKL') {
            return { success: true };
        } else if (licenseKey.startsWith('TEST')) {
            return { success: true };
        } else {
            return { 
                success: false, 
                errorMessage: 'License Key无效或已过期，请检查后重试' 
            };
        }
    }

    /**
     * 执行取消激活
     */
    executeCancelActivation() {
        if (this.getProperty('isActivating')) {
            // 取消正在进行的激活
            this.setProperty('isActivating', false);
            this.setProperty('activationStatus', 'idle');
            this.setProperty('activationMessage', '激活已取消');
        } else {
            // 退出应用
            this.exitApplication();
        }
    }

    /**
     * 执行清除输入
     */
    executeClearInput() {
        this.setProperty('licenseKey', '');
        this.setProperty('companyName', '');
        this.setProperty('contactEmail', '');
        this.setProperty('licenseKeyError', '');
        this.setProperty('emailError', '');
        this.setProperty('activationStatus', 'idle');
        this.setProperty('activationMessage', '请输入License Key进行激活');
        
        // 清除输入框
        const inputs = ['license-key-input', 'company-name-input', 'contact-email-input'];
        inputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.value = '';
        });
        
        // 聚焦到License Key输入框
        const licenseKeyInput = document.getElementById('license-key-input');
        if (licenseKeyInput) licenseKeyInput.focus();
    }

    /**
     * 执行获取帮助
     */
    executeGetHelp() {
        // 显示帮助模态框或导航到帮助页面
        this.dispatchEvent('showHelp', {
            topic: 'license-activation'
        });
    }

    /**
     * 执行联系技术支持
     */
    executeContactSupport() {
        // 显示技术支持模态框或导航到支持页面
        this.dispatchEvent('showSupport', {
            issue: 'license-activation',
            licenseKey: this.getProperty('licenseKey'),
            email: this.getProperty('contactEmail')
        });
    }

    /**
     * 导航到主应用程序
     */
    navigateToMainApplication() {
        this.dispatchEvent('navigateToMainApp', {
            licenseInfo: {
                key: this.getProperty('licenseKey'),
                company: this.getProperty('companyName'),
                email: this.getProperty('contactEmail')
            }
        });
    }

    /**
     * 退出应用程序
     */
    exitApplication() {
        this.dispatchEvent('exitApplication');
    }

    /**
     * 初始化
     */
    async initialize() {
        console.log('📊 ViewModel初始化完成');
    }

    /**
     * 销毁
     */
    destroy() {
        this._properties.clear();
        this._bindings.clear();
        this._validators.clear();
        console.log('📊 ViewModel已销毁');
    }
}
