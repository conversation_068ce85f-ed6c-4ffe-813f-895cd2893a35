<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="商用空调监控调试软件 - License激活页面">
    <title>License激活 - 商用空调监控调试软件</title>
    
    <!-- Fluent Design CSS样式 -->
    <link rel="stylesheet" href="css/design-system.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/layouts.css">
    <link rel="stylesheet" href="css/themes.css">
    <link rel="stylesheet" href="css/responsive.css">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="assets/fonts/SegoeUI-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="assets/fonts/CascadiaCode-Regular.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/icons/license-key.svg">
    <link rel="icon" type="image/png" href="assets/icons/license-key-32.png">
</head>
<body class="license-activation-page" data-theme="light" data-user-mode="basic">
    <!-- 跳过链接 - 无障碍支持 -->
    <a href="#main-content" class="skip-link">跳转到主要内容</a>
    
    <!-- 主要内容容器 -->
    <main id="main-content" class="activation-container" role="main">
        <!-- 页面头部 -->
        <header class="activation-header" role="banner">
            <div class="activation-title">
                <svg class="icon icon-hero icon-license-key" aria-hidden="true" focusable="false">
                    <use href="assets/icons/sprite.svg#license-key"></use>
                </svg>
                <h1>License激活</h1>
            </div>
            <p class="activation-subtitle">
                欢迎使用商用空调监控调试软件，请输入您的License Key进行激活
            </p>
        </header>
        
        <!-- 激活表单 -->
        <section class="activation-form" role="form" aria-labelledby="activation-title">
            <!-- License Key输入组 -->
            <div class="form-group license-key-group">
                <label for="license-key-input" class="input-label required">
                    License Key
                </label>
                <div class="license-key-input-wrapper">
                    <input 
                        type="text" 
                        id="license-key-input" 
                        class="input-license-key" 
                        placeholder="XXXX-XXXX-XXXX-XXXX-XXXX"
                        maxlength="24"
                        autocomplete="off"
                        spellcheck="false"
                        aria-describedby="license-key-help license-key-error"
                        aria-required="true"
                        data-testid="license-key-input"
                    >
                    <button 
                        type="button" 
                        class="license-key-clear" 
                        aria-label="清除License Key"
                        tabindex="-1"
                        data-testid="clear-license-key"
                    >
                        <svg class="icon icon-small" aria-hidden="true">
                            <use href="assets/icons/sprite.svg#clear"></use>
                        </svg>
                    </button>
                </div>
                <div id="license-key-help" class="input-help">
                    请输入20位License Key，支持拖拽文件或粘贴文本
                </div>
                <div id="license-key-error" class="input-error" role="alert" aria-live="polite">
                    <!-- 错误信息将通过JavaScript动态显示 -->
                </div>
            </div>
            
            <!-- 公司名称输入组 -->
            <div class="form-group">
                <label for="company-name-input" class="input-label">
                    公司名称 <span class="optional">(可选)</span>
                </label>
                <input 
                    type="text" 
                    id="company-name-input" 
                    class="input-text" 
                    placeholder="请输入您的公司名称"
                    maxlength="100"
                    aria-describedby="company-name-help"
                    data-testid="company-name-input"
                >
                <div id="company-name-help" class="input-help">
                    公司名称将用于License记录和技术支持
                </div>
            </div>
            
            <!-- 联系邮箱输入组 -->
            <div class="form-group">
                <label for="contact-email-input" class="input-label">
                    联系邮箱 <span class="optional">(可选)</span>
                </label>
                <input 
                    type="email" 
                    id="contact-email-input" 
                    class="input-text" 
                    placeholder="请输入您的联系邮箱"
                    maxlength="100"
                    aria-describedby="contact-email-help contact-email-error"
                    data-testid="contact-email-input"
                >
                <div id="contact-email-help" class="input-help">
                    邮箱地址将用于技术支持和重要通知
                </div>
                <div id="contact-email-error" class="input-error" role="alert" aria-live="polite">
                    <!-- 邮箱验证错误信息 -->
                </div>
            </div>
        </section>
        
        <!-- 激活状态显示 -->
        <section class="activation-status" role="status" aria-live="polite" aria-labelledby="status-title">
            <h2 id="status-title" class="sr-only">激活状态</h2>
            <div class="status-icon-container">
                <svg class="status-icon icon-extra-large" aria-hidden="true">
                    <use href="assets/icons/sprite.svg#info"></use>
                </svg>
                <div class="status-progress" style="display: none;">
                    <div class="progress-ring">
                        <svg class="progress-ring-svg" width="32" height="32">
                            <circle class="progress-ring-circle" cx="16" cy="16" r="14"></circle>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="status-text" id="status-message">
                请输入License Key进行激活
            </div>
        </section>
        
        <!-- 操作按钮区域 -->
        <section class="activation-actions" role="group" aria-labelledby="actions-title">
            <h2 id="actions-title" class="sr-only">操作按钮</h2>
            
            <!-- 主要操作按钮 -->
            <div class="primary-actions">
                <button 
                    type="button" 
                    id="activate-button" 
                    class="button-primary"
                    disabled
                    aria-describedby="activate-help"
                    data-testid="activate-button"
                >
                    <span class="button-content">
                        <svg class="icon icon-medium" aria-hidden="true">
                            <use href="assets/icons/sprite.svg#activate"></use>
                        </svg>
                        <span class="button-text">激活License</span>
                    </span>
                </button>
                
                <button 
                    type="button" 
                    id="cancel-button" 
                    class="button-secondary"
                    aria-describedby="cancel-help"
                    data-testid="cancel-button"
                >
                    <span class="button-content">
                        <svg class="icon icon-medium" aria-hidden="true">
                            <use href="assets/icons/sprite.svg#cancel"></use>
                        </svg>
                        <span class="button-text">取消</span>
                    </span>
                </button>
            </div>
            
            <!-- 次要操作按钮 -->
            <div class="secondary-actions">
                <button 
                    type="button" 
                    id="help-button" 
                    class="button-text"
                    aria-describedby="help-description"
                    data-testid="help-button"
                >
                    <svg class="icon icon-small" aria-hidden="true">
                        <use href="assets/icons/sprite.svg#help"></use>
                    </svg>
                    获取帮助
                </button>
                
                <button 
                    type="button" 
                    id="support-button" 
                    class="button-text"
                    aria-describedby="support-description"
                    data-testid="support-button"
                >
                    <svg class="icon icon-small" aria-hidden="true">
                        <use href="assets/icons/sprite.svg#support"></use>
                    </svg>
                    联系技术支持
                </button>
            </div>
        </section>
        
        <!-- 隐藏的帮助描述 -->
        <div class="sr-only">
            <div id="activate-help">点击激活您的License，需要先输入有效的License Key</div>
            <div id="cancel-help">取消激活操作并退出程序</div>
            <div id="help-description">获取License激活的帮助信息和操作指南</div>
            <div id="support-description">联系技术支持团队获取帮助</div>
        </div>
    </main>
    
    <!-- 页面底部 -->
    <footer class="activation-footer" role="contentinfo">
        <div class="footer-copyright">
            © 2024 商用空调监控调试软件. 保留所有权利.
        </div>
        <div class="footer-support">
            <a href="#" class="footer-link" data-action="privacy">隐私政策</a>
            <a href="#" class="footer-link" data-action="terms">使用条款</a>
            <a href="#" class="footer-link" data-action="contact">联系我们</a>
        </div>
    </footer>
    
    <!-- 模态框容器 -->
    <div id="modal-container" class="modal-container" role="dialog" aria-hidden="true">
        <!-- 模态框内容将通过JavaScript动态加载 -->
    </div>
    
    <!-- 通知容器 -->
    <div id="notification-container" class="notification-container" aria-live="polite" aria-atomic="true">
        <!-- 通知消息将通过JavaScript动态显示 -->
    </div>
    
    <!-- 拖拽覆盖层 -->
    <div id="drop-overlay" class="drop-overlay" style="display: none;">
        <div class="drop-zone">
            <svg class="icon icon-hero" aria-hidden="true">
                <use href="assets/icons/sprite.svg#upload"></use>
            </svg>
            <div class="drop-text">
                <div class="drop-title">拖拽License文件到此处</div>
                <div class="drop-subtitle">支持 .lic 和 .license 文件格式</div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript模块 -->
    <script type="module" src="js/app.js"></script>
    <script type="module" src="js/components.js"></script>
    <script type="module" src="js/data-binding.js"></script>
    <script type="module" src="js/event-handlers.js"></script>
    <script type="module" src="js/navigation.js"></script>
    <script type="module" src="js/utils.js"></script>
    
    <!-- 无障碍增强脚本 -->
    <script type="module" src="js/accessibility.js"></script>
    
    <!-- 性能监控 -->
    <script>
        // 页面加载性能监控
        window.addEventListener('load', function() {
            if ('performance' in window) {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                console.log(`页面加载时间: ${loadTime}ms`);
                
                // 发送性能数据（生产环境中可发送到分析服务）
                if (loadTime > 3000) {
                    console.warn('页面加载时间超过3秒，需要优化');
                }
            }
        });
    </script>
</body>
</html>
