using System;
using System.ComponentModel.DataAnnotations;

namespace AirConditioningMonitor.License.Models
{
    /// <summary>
    /// License激活请求模型
    /// 包含激活所需的所有信息
    /// </summary>
    public class ActivationRequest
    {
        /// <summary>
        /// License Key（20位字符，不含分隔符）
        /// </summary>
        [Required(ErrorMessage = "License Key不能为空")]
        [StringLength(20, MinimumLength = 20, ErrorMessage = "License Key必须为20位字符")]
        [RegularExpression(@"^[A-Z0-9]{20}$", ErrorMessage = "License Key只能包含大写字母和数字")]
        public string LicenseKey { get; set; } = string.Empty;

        /// <summary>
        /// 公司名称（可选）
        /// </summary>
        [StringLength(100, ErrorMessage = "公司名称不能超过100个字符")]
        public string CompanyName { get; set; } = string.Empty;

        /// <summary>
        /// 联系邮箱（可选，用于技术支持）
        /// </summary>
        [EmailAddress(ErrorMessage = "请输入有效的邮箱地址")]
        [StringLength(100, ErrorMessage = "邮箱地址不能超过100个字符")]
        public string ContactEmail { get; set; } = string.Empty;

        /// <summary>
        /// 机器标识（用于绑定设备）
        /// </summary>
        [Required(ErrorMessage = "机器标识不能为空")]
        public string MachineId { get; set; } = string.Empty;

        /// <summary>
        /// 激活时间
        /// </summary>
        public DateTime ActivationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 软件版本
        /// </summary>
        public string SoftwareVersion { get; set; } = "1.0.0";

        /// <summary>
        /// 操作系统信息
        /// </summary>
        public string OperatingSystem { get; set; } = Environment.OSVersion.ToString();

        /// <summary>
        /// 验证激活请求的有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            // 验证License Key
            if (string.IsNullOrWhiteSpace(LicenseKey))
            {
                result.AddError("LicenseKey", "License Key不能为空");
            }
            else if (LicenseKey.Length != 20)
            {
                result.AddError("LicenseKey", "License Key必须为20位字符");
            }
            else if (!System.Text.RegularExpressions.Regex.IsMatch(LicenseKey, @"^[A-Z0-9]{20}$"))
            {
                result.AddError("LicenseKey", "License Key只能包含大写字母和数字");
            }

            // 验证机器标识
            if (string.IsNullOrWhiteSpace(MachineId))
            {
                result.AddError("MachineId", "机器标识不能为空");
            }

            // 验证邮箱（如果提供）
            if (!string.IsNullOrWhiteSpace(ContactEmail))
            {
                var emailPattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
                if (!System.Text.RegularExpressions.Regex.IsMatch(ContactEmail, emailPattern))
                {
                    result.AddError("ContactEmail", "请输入有效的邮箱地址");
                }
            }

            return result;
        }

        /// <summary>
        /// 生成激活请求的哈希值（用于安全验证）
        /// </summary>
        /// <returns>哈希值</returns>
        public string GenerateHash()
        {
            var data = $"{LicenseKey}{MachineId}{ActivationTime:yyyyMMddHHmmss}";
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(data));
            return Convert.ToBase64String(hashBytes);
        }
    }

    /// <summary>
    /// License激活响应模型
    /// </summary>
    public class ActivationResponse
    {
        /// <summary>
        /// 激活是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息（激活失败时）
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; } = string.Empty;

        /// <summary>
        /// 激活后的License信息
        /// </summary>
        public LicenseInfo LicenseInfo { get; set; } = new();

        /// <summary>
        /// 权限级别
        /// </summary>
        public PermissionLevel PermissionLevel { get; set; }

        /// <summary>
        /// License到期日期
        /// </summary>
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// 激活令牌（用于后续验证）
        /// </summary>
        public string ActivationToken { get; set; } = string.Empty;

        /// <summary>
        /// 服务器响应时间
        /// </summary>
        public DateTime ResponseTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建成功响应
        /// </summary>
        public static ActivationResponse Success(LicenseInfo licenseInfo, string activationToken)
        {
            return new ActivationResponse
            {
                IsSuccess = true,
                LicenseInfo = licenseInfo,
                PermissionLevel = licenseInfo.PermissionLevel,
                ExpiryDate = licenseInfo.ExpiryDate,
                ActivationToken = activationToken
            };
        }

        /// <summary>
        /// 创建失败响应
        /// </summary>
        public static ActivationResponse Failure(string errorMessage, string errorCode = "")
        {
            return new ActivationResponse
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorCode = errorCode
            };
        }
    }

    /// <summary>
    /// License信息模型
    /// </summary>
    public class LicenseInfo
    {
        /// <summary>
        /// License唯一标识
        /// </summary>
        public string LicenseId { get; set; } = string.Empty;

        /// <summary>
        /// License Key
        /// </summary>
        public string LicenseKey { get; set; } = string.Empty;

        /// <summary>
        /// License类型
        /// </summary>
        public LicenseType LicenseType { get; set; }

        /// <summary>
        /// 权限级别
        /// </summary>
        public PermissionLevel PermissionLevel { get; set; }

        /// <summary>
        /// 发行日期
        /// </summary>
        public DateTime IssueDate { get; set; }

        /// <summary>
        /// 到期日期
        /// </summary>
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// 授权公司
        /// </summary>
        public string LicensedCompany { get; set; } = string.Empty;

        /// <summary>
        /// 绑定的机器标识
        /// </summary>
        public string BoundMachineId { get; set; } = string.Empty;

        /// <summary>
        /// 最大并发用户数
        /// </summary>
        public int MaxConcurrentUsers { get; set; } = 1;

        /// <summary>
        /// 是否允许离线使用
        /// </summary>
        public bool AllowOfflineUse { get; set; } = true;

        /// <summary>
        /// License状态
        /// </summary>
        public LicenseStatus Status { get; set; } = LicenseStatus.Active;

        /// <summary>
        /// 检查License是否有效
        /// </summary>
        public bool IsValid => Status == LicenseStatus.Active && DateTime.Now <= ExpiryDate;

        /// <summary>
        /// 检查License是否即将到期（30天内）
        /// </summary>
        public bool IsExpiringSoon => IsValid && (ExpiryDate - DateTime.Now).TotalDays <= 30;

        /// <summary>
        /// 获取剩余天数
        /// </summary>
        public int RemainingDays => IsValid ? Math.Max(0, (int)(ExpiryDate - DateTime.Now).TotalDays) : 0;
    }

    /// <summary>
    /// License类型枚举
    /// </summary>
    public enum LicenseType
    {
        Trial,      // 试用版
        Standard,   // 标准版
        Professional, // 专业版
        Enterprise  // 企业版
    }

    /// <summary>
    /// 权限级别枚举
    /// </summary>
    public enum PermissionLevel
    {
        Basic = 1,      // 普通权限
        Maintenance = 2, // 售后权限
        Manager = 3,    // 主管权限
        Developer = 4   // 研发权限
    }

    /// <summary>
    /// License状态枚举
    /// </summary>
    public enum LicenseStatus
    {
        Active,     // 激活状态
        Expired,    // 已过期
        Suspended,  // 已暂停
        Revoked     // 已撤销
    }

    /// <summary>
    /// 验证结果类
    /// </summary>
    public class ValidationResult
    {
        private readonly Dictionary<string, List<string>> _errors = new();

        /// <summary>
        /// 是否有效（无错误）
        /// </summary>
        public bool IsValid => !_errors.Any();

        /// <summary>
        /// 所有错误信息
        /// </summary>
        public IReadOnlyDictionary<string, List<string>> Errors => _errors;

        /// <summary>
        /// 添加错误信息
        /// </summary>
        public void AddError(string propertyName, string errorMessage)
        {
            if (!_errors.ContainsKey(propertyName))
            {
                _errors[propertyName] = new List<string>();
            }
            _errors[propertyName].Add(errorMessage);
        }

        /// <summary>
        /// 获取指定属性的错误信息
        /// </summary>
        public IEnumerable<string> GetErrors(string propertyName)
        {
            return _errors.ContainsKey(propertyName) ? _errors[propertyName] : Enumerable.Empty<string>();
        }

        /// <summary>
        /// 获取所有错误信息的字符串表示
        /// </summary>
        public string GetAllErrorsAsString()
        {
            return string.Join(Environment.NewLine, 
                _errors.SelectMany(kvp => kvp.Value.Select(error => $"{kvp.Key}: {error}")));
        }
    }
}
