/* 
 * 布局样式 - License激活页面
 * 响应式布局和空间设计
 */

/* 页面容器 */
.license-activation-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-secondary);
  padding: var(--spacing-md);
  font-family: var(--font-family-primary);
}

/* 主内容容器 */
.activation-container {
  width: 100%;
  max-width: var(--content-width-max);
  min-width: var(--content-width-min);
  background-color: var(--background-primary);
  border-radius: var(--border-radius-extra-large);
  box-shadow: var(--elevation-3);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.activation-header {
  text-align: center;
  padding: var(--spacing-xxl) var(--spacing-xl) var(--spacing-lg);
  background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
  border-bottom: 1px solid var(--border-primary);
}

/* 主标题 */
.activation-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.activation-title .icon-hero {
  color: var(--primary-color);
  filter: drop-shadow(0 2px 4px var(--shadow-light));
}

.activation-title h1 {
  color: var(--text-primary);
  font-size: var(--font-size-h1);
  font-weight: var(--font-weight-h1);
  line-height: var(--line-height-h1);
  letter-spacing: var(--letter-spacing-h1);
  margin: 0;
}

/* 副标题 */
.activation-subtitle {
  color: var(--text-secondary);
  font-size: var(--font-size-body-large);
  font-weight: var(--font-weight-body-large);
  line-height: var(--line-height-body-large);
  margin: 0;
  max-width: 360px;
  margin-left: auto;
  margin-right: auto;
}

/* 表单区域 */
.activation-form {
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* License Key输入组特殊布局 */
.license-key-group {
  position: relative;
}

/* 激活状态显示 */
.activation-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  margin: 0 var(--spacing-xl);
  border-radius: var(--border-radius-large);
  background-color: var(--background-secondary);
  border: 1px solid var(--border-primary);
  transition: all var(--duration-normal) var(--easing-ease-out);
  position: relative;
  overflow: hidden;
}

/* 状态图标容器 */
.status-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
}

/* 状态进度指示器 */
.status-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 状态文字 */
.status-text {
  text-align: center;
  color: var(--text-primary);
  font-size: var(--font-size-status);
  font-weight: var(--font-weight-status);
  line-height: var(--line-height-status);
  max-width: 300px;
}

/* 状态变体 */
.activation-status.processing {
  background-color: var(--info-background);
  border-color: var(--info-border);
  animation: glow-pulse 2s ease-in-out infinite;
}

.activation-status.success {
  background-color: var(--success-background);
  border-color: var(--success-border);
}

.activation-status.error {
  background-color: var(--error-background);
  border-color: var(--error-border);
}

/* 操作按钮区域 */
.activation-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xxl);
}

/* 主要操作按钮 */
.primary-actions {
  display: flex;
  gap: var(--spacing-md);
}

.primary-actions .button-primary {
  flex: 1;
  min-width: 120px;
}

.primary-actions .button-secondary {
  flex: 0 0 auto;
  min-width: 80px;
}

/* 次要操作按钮 */
.secondary-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.secondary-actions .button-text {
  flex: 0 0 auto;
}

/* 页面底部 */
.activation-footer {
  padding: var(--spacing-lg) var(--spacing-xl);
  background-color: var(--background-secondary);
  border-top: 1px solid var(--border-primary);
  text-align: center;
  margin-top: auto;
}

/* 版权信息 */
.footer-copyright {
  color: var(--text-tertiary);
  font-size: var(--font-size-body-small);
  font-weight: var(--font-weight-body-small);
  line-height: var(--line-height-body-small);
  margin-bottom: var(--spacing-sm);
}

/* 技术支持链接 */
.footer-support {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.footer-link {
  color: var(--text-secondary);
  font-size: var(--font-size-body-small);
  text-decoration: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-small);
  transition: all var(--duration-fast) var(--easing-ease-out);
}

.footer-link:hover {
  color: var(--primary-color);
  background-color: var(--background-tertiary);
  text-decoration: underline;
}

/* 模态框容器 */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) var(--easing-ease-out);
}

.modal-container[aria-hidden="false"] {
  opacity: 1;
  visibility: visible;
}

/* 通知容器 */
.notification-container {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: var(--z-index-notification);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-width: 400px;
  pointer-events: none;
}

/* 拖拽覆盖层 */
.drop-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 120, 212, 0.1);
  backdrop-filter: blur(4px);
  z-index: var(--z-index-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) var(--easing-ease-out);
}

.drop-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* 拖拽区域 */
.drop-zone {
  background-color: var(--background-primary);
  border: 2px dashed var(--primary-color);
  border-radius: var(--border-radius-extra-large);
  padding: var(--spacing-xxl);
  text-align: center;
  max-width: 400px;
  box-shadow: var(--elevation-4);
}

.drop-zone .icon-hero {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.drop-text {
  color: var(--text-primary);
}

.drop-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-h3);
  margin-bottom: var(--spacing-xs);
}

.drop-subtitle {
  font-size: var(--font-size-body);
  color: var(--text-secondary);
}

/* 动画定义 */
@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(0, 120, 212, 0.3);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(0, 120, 212, 0);
  }
}

/* 响应式布局 - 移动设备 */
@media (max-width: 768px) {
  .license-activation-page {
    padding: var(--spacing-sm);
    align-items: flex-start;
    padding-top: var(--spacing-lg);
  }
  
  .activation-container {
    max-width: 100%;
    border-radius: var(--border-radius-large);
  }
  
  .activation-header {
    padding: var(--spacing-lg) var(--spacing-md);
  }
  
  .activation-title {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .activation-title .icon-hero {
    width: var(--icon-size-extra-large);
    height: var(--icon-size-extra-large);
  }
  
  .activation-title h1 {
    font-size: var(--font-size-h2);
  }
  
  .activation-form {
    padding: var(--spacing-lg) var(--spacing-md);
    gap: var(--spacing-md);
  }
  
  .activation-status {
    margin: 0 var(--spacing-md);
  }
  
  .primary-actions {
    flex-direction: column;
  }
  
  .secondary-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .activation-actions {
    padding: var(--spacing-lg) var(--spacing-md);
  }
  
  .notification-container {
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    left: var(--spacing-sm);
    max-width: none;
  }
}

/* 响应式布局 - 平板设备 */
@media (min-width: 769px) and (max-width: 1024px) {
  .activation-container {
    max-width: var(--content-width-optimal);
  }
  
  .activation-header {
    padding: var(--spacing-xl) var(--spacing-lg);
  }
  
  .activation-form {
    padding: var(--spacing-xl) var(--spacing-lg);
  }
  
  .activation-actions {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-xl);
  }
}

/* 响应式布局 - 桌面设备 */
@media (min-width: 1025px) {
  .license-activation-page {
    padding: var(--spacing-xl);
  }
  
  .activation-container {
    max-width: var(--content-width-max);
  }
  
  /* 桌面设备可以显示更多辅助信息 */
  .activation-help-sidebar {
    display: block;
    position: absolute;
    right: -280px;
    top: 0;
    width: 260px;
    height: 100%;
    background-color: var(--background-secondary);
    border-left: 1px solid var(--border-primary);
    padding: var(--spacing-lg);
  }
}

/* 多层次用户布局适配 */
[data-user-mode="basic"] .activation-form {
  gap: var(--spacing-xl);
}

[data-user-mode="basic"] .form-group {
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
}

[data-user-mode="basic"] .activation-actions {
  gap: var(--spacing-lg);
}

[data-user-mode="professional"] .activation-form {
  gap: var(--spacing-md);
  padding: var(--spacing-lg) var(--spacing-md);
}

[data-user-mode="professional"] .form-group {
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
}

/* 打印样式 */
@media print {
  .license-activation-page {
    background: white;
    padding: 0;
  }
  
  .activation-container {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .activation-actions,
  .activation-footer {
    display: none;
  }
}
