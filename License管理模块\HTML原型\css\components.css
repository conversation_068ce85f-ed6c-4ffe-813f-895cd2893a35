/* 
 * 组件样式 - License激活页面
 * 基于Fluent Design System的组件实现
 */

/* 按钮组件 */
.button-primary,
.button-secondary,
.button-text {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  min-height: 32px;
  border: 1px solid transparent;
  border-radius: var(--border-radius-medium);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-button);
  font-weight: var(--font-weight-button);
  line-height: var(--line-height-button);
  letter-spacing: var(--letter-spacing-button);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease-out);
  outline: none;
  user-select: none;
  position: relative;
  overflow: hidden;
}

/* 主要按钮 */
.button-primary {
  background-color: var(--primary-color);
  color: #FFFFFF;
  border-color: var(--primary-color);
  box-shadow: var(--elevation-1);
}

.button-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
  box-shadow: var(--elevation-2);
  transform: translateY(-1px);
}

.button-primary:active:not(:disabled) {
  background-color: var(--primary-pressed);
  border-color: var(--primary-pressed);
  box-shadow: var(--elevation-1);
  transform: translateY(0);
}

.button-primary:disabled {
  background-color: var(--primary-disabled);
  border-color: var(--primary-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 次要按钮 */
.button-secondary {
  background-color: var(--background-secondary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.button-secondary:hover:not(:disabled) {
  background-color: var(--background-tertiary);
  border-color: var(--border-secondary);
  box-shadow: var(--elevation-1);
}

.button-secondary:active:not(:disabled) {
  background-color: var(--background-quaternary);
  transform: translateY(1px);
}

/* 文本按钮 */
.button-text {
  background-color: transparent;
  color: var(--primary-color);
  border: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  min-height: 24px;
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-body);
}

.button-text:hover:not(:disabled) {
  background-color: var(--background-tertiary);
  color: var(--primary-hover);
  text-decoration: underline;
}

.button-text:active:not(:disabled) {
  color: var(--primary-pressed);
}

/* 按钮内容布局 */
.button-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.button-text {
  white-space: nowrap;
}

/* 输入框组件 */
.input-license-key,
.input-text {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-primary);
  border-radius: var(--border-radius-medium);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-body);
  line-height: var(--line-height-body);
  color: var(--text-primary);
  background-color: var(--background-primary);
  transition: all var(--duration-fast) var(--easing-ease-out);
  outline: none;
}

/* License Key专用输入框 */
.input-license-key {
  font-family: var(--font-family-monospace);
  font-size: var(--font-size-license-key);
  font-weight: var(--font-weight-license-key);
  line-height: var(--line-height-license-key);
  letter-spacing: var(--letter-spacing-license-key);
  text-transform: uppercase;
  min-height: 44px;
  padding-right: 48px; /* 为清除按钮留空间 */
}

/* 输入框状态 */
.input-license-key:hover,
.input-text:hover {
  border-color: var(--border-secondary);
  box-shadow: var(--elevation-1);
}

.input-license-key:focus,
.input-text:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 1px var(--border-focus);
}

.input-license-key.error,
.input-text.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 1px var(--error-color);
}

.input-license-key.success,
.input-text.success {
  border-color: var(--success-color);
  box-shadow: 0 0 0 1px var(--success-color);
}

/* 占位符样式 */
.input-license-key::placeholder,
.input-text::placeholder {
  color: var(--text-placeholder);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-placeholder);
  letter-spacing: normal;
  text-transform: none;
}

/* License Key输入框包装器 */
.license-key-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

/* 清除按钮 */
.license-key-clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--border-radius-small);
  transition: all var(--duration-fast) var(--easing-ease-out);
  opacity: 0;
  visibility: hidden;
}

.license-key-input-wrapper:hover .license-key-clear,
.license-key-input-wrapper:focus-within .license-key-clear {
  opacity: 1;
  visibility: visible;
}

.license-key-clear:hover {
  color: var(--text-secondary);
  background-color: var(--background-tertiary);
}

.license-key-clear:active {
  color: var(--text-primary);
  background-color: var(--background-quaternary);
}

/* 表单组件 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-lg);
}

/* 输入框标签 */
.input-label {
  display: block;
  color: var(--text-primary);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-h4);
  line-height: var(--line-height-h4);
  margin-bottom: var(--spacing-xs);
}

.input-label.required::after {
  content: ' *';
  color: var(--error-color);
}

.optional {
  color: var(--text-tertiary);
  font-weight: var(--font-weight-body);
}

/* 帮助文字 */
.input-help {
  display: block;
  color: var(--text-tertiary);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-body-small);
  font-weight: var(--font-weight-body-small);
  line-height: var(--line-height-body-small);
  margin-top: var(--spacing-xs);
}

/* 错误提示 */
.input-error {
  display: block;
  color: var(--error-color);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-error);
  font-weight: var(--font-weight-error);
  line-height: var(--line-height-error);
  margin-top: var(--spacing-xs);
  opacity: 0;
  transform: translateY(-4px);
  transition: all var(--duration-fast) var(--easing-ease-out);
}

.input-error:not(:empty) {
  opacity: 1;
  transform: translateY(0);
}

/* 图标组件 */
.icon {
  display: inline-block;
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
  fill: currentColor;
  vertical-align: middle;
  flex-shrink: 0;
}

.icon-small { 
  width: var(--icon-size-small); 
  height: var(--icon-size-small); 
}

.icon-medium { 
  width: var(--icon-size-medium); 
  height: var(--icon-size-medium); 
}

.icon-large { 
  width: var(--icon-size-large); 
  height: var(--icon-size-large); 
}

.icon-extra-large { 
  width: var(--icon-size-extra-large); 
  height: var(--icon-size-extra-large); 
}

.icon-hero { 
  width: var(--icon-size-hero); 
  height: var(--icon-size-hero); 
}

/* 状态指示器 */
.status-icon {
  transition: all var(--duration-normal) var(--easing-ease-out);
}

.status-icon.processing {
  color: var(--info-color);
  animation: pulse 1.5s ease-in-out infinite;
}

.status-icon.success {
  color: var(--success-color);
  animation: success-scale var(--duration-normal) var(--easing-bounce);
}

.status-icon.error {
  color: var(--error-color);
  animation: error-shake 0.4s ease-in-out;
}

/* 进度指示器 */
.progress-ring {
  width: 32px;
  height: 32px;
  position: relative;
}

.progress-ring-svg {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  fill: none;
  stroke: var(--primary-color);
  stroke-width: 2;
  stroke-linecap: round;
  stroke-dasharray: 88;
  stroke-dashoffset: 88;
  animation: progress-spin 1s linear infinite;
}

/* 动画定义 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes success-scale {
  0% { transform: scale(0.8); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes error-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

@keyframes progress-spin {
  0% { stroke-dashoffset: 88; }
  100% { stroke-dashoffset: 0; }
}

/* 触控设备优化 */
@media (pointer: coarse) {
  .button-primary,
  .button-secondary {
    min-height: var(--touch-target-minimum);
    padding: 12px var(--spacing-lg);
  }
  
  .input-license-key,
  .input-text {
    min-height: var(--touch-target-minimum);
    padding: 12px 16px;
  }
  
  .license-key-clear {
    min-width: var(--touch-target-minimum);
    min-height: var(--touch-target-minimum);
    padding: 8px;
  }
  
  .button-text {
    min-height: 36px;
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .button-primary {
    border: 2px solid var(--text-primary);
  }
  
  .input-license-key,
  .input-text {
    border: 2px solid var(--text-primary);
  }
  
  .input-license-key:focus,
  .input-text:focus {
    border: 2px solid var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color);
  }
}
