/**
 * License激活页面 - 事件处理模块
 * 处理用户交互事件和系统事件
 */

import { Utils } from './utils.js';

/**
 * 事件处理器类
 */
export class EventHandlers {
    constructor(viewModel) {
        this.viewModel = viewModel;
        this.eventListeners = new Map();
        this.dragCounter = 0;
        this.isInitialized = false;
    }

    /**
     * 初始化事件处理器
     */
    async initialize() {
        console.log('🎮 事件处理器初始化...');
        this.isInitialized = true;
    }

    /**
     * 设置表单事件
     */
    setupFormEvents() {
        // License Key输入框事件
        this.setupLicenseKeyEvents();
        
        // 公司名称输入框事件
        this.setupCompanyNameEvents();
        
        // 联系邮箱输入框事件
        this.setupContactEmailEvents();
        
        // 表单提交事件
        this.setupFormSubmitEvents();
    }

    /**
     * 设置License Key输入框事件
     */
    setupLicenseKeyEvents() {
        const licenseKeyInput = document.getElementById('license-key-input');
        const clearButton = document.querySelector('.license-key-clear');
        
        if (licenseKeyInput) {
            // 输入事件
            this.addEventListener(licenseKeyInput, 'input', (e) => {
                this.handleLicenseKeyInput(e);
            });

            // 粘贴事件
            this.addEventListener(licenseKeyInput, 'paste', (e) => {
                this.handleLicenseKeyPaste(e);
            });

            // 键盘事件
            this.addEventListener(licenseKeyInput, 'keydown', (e) => {
                this.handleLicenseKeyKeyDown(e);
            });

            // 焦点事件
            this.addEventListener(licenseKeyInput, 'focus', (e) => {
                this.handleLicenseKeyFocus(e);
            });

            this.addEventListener(licenseKeyInput, 'blur', (e) => {
                this.handleLicenseKeyBlur(e);
            });
        }

        if (clearButton) {
            this.addEventListener(clearButton, 'click', (e) => {
                this.handleClearButtonClick(e);
            });
        }
    }

    /**
     * 处理License Key输入
     */
    handleLicenseKeyInput(e) {
        let value = e.target.value;
        
        // 只允许字母和数字
        value = value.replace(/[^A-Za-z0-9-]/g, '');
        
        // 转换为大写
        value = value.toUpperCase();
        
        // 格式化为XXXX-XXXX-XXXX-XXXX-XXXX
        const cleanValue = value.replace(/-/g, '');
        if (cleanValue.length <= 20) {
            const formatted = cleanValue.replace(/(.{4})/g, '$1-').replace(/-$/, '');
            
            if (e.target.value !== formatted) {
                const cursorPosition = e.target.selectionStart;
                e.target.value = formatted;
                
                // 恢复光标位置
                const newCursorPosition = Math.min(cursorPosition, formatted.length);
                e.target.setSelectionRange(newCursorPosition, newCursorPosition);
            }
            
            this.viewModel.setProperty('licenseKey', formatted);
        }
        
        // 更新清除按钮显示
        this.updateClearButtonVisibility();
    }

    /**
     * 处理License Key粘贴
     */
    handleLicenseKeyPaste(e) {
        e.preventDefault();
        
        const pastedText = (e.clipboardData || window.clipboardData).getData('text');
        const cleanText = pastedText.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
        
        if (cleanText.length <= 20) {
            const formatted = cleanText.replace(/(.{4})/g, '$1-').replace(/-$/, '');
            e.target.value = formatted;
            this.viewModel.setProperty('licenseKey', formatted);
            this.updateClearButtonVisibility();
        }
    }

    /**
     * 处理License Key键盘事件
     */
    handleLicenseKeyKeyDown(e) {
        // 允许的特殊键
        const allowedKeys = [
            'Backspace', 'Delete', 'Tab', 'Enter', 'Escape',
            'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
            'Home', 'End', 'PageUp', 'PageDown'
        ];
        
        // Ctrl组合键
        if (e.ctrlKey && ['a', 'c', 'v', 'x', 'z', 'y'].includes(e.key.toLowerCase())) {
            return;
        }
        
        // 允许的键或字母数字
        if (allowedKeys.includes(e.key) || /^[A-Za-z0-9]$/.test(e.key)) {
            return;
        }
        
        // 阻止其他键
        e.preventDefault();
    }

    /**
     * 处理License Key焦点获得
     */
    handleLicenseKeyFocus(e) {
        e.target.classList.add('focused');
        this.updateClearButtonVisibility();
        
        // 显示输入提示
        this.showInputHint('请输入20位License Key');
    }

    /**
     * 处理License Key焦点失去
     */
    handleLicenseKeyBlur(e) {
        e.target.classList.remove('focused');
        this.hideInputHint();
        
        // 验证输入
        const value = e.target.value;
        if (value) {
            this.viewModel.validateProperty('licenseKey', value);
        }
    }

    /**
     * 处理清除按钮点击
     */
    handleClearButtonClick(e) {
        e.preventDefault();
        this.viewModel.commands.clear.execute();
        this.updateClearButtonVisibility();
    }

    /**
     * 更新清除按钮可见性
     */
    updateClearButtonVisibility() {
        const clearButton = document.querySelector('.license-key-clear');
        const licenseKeyInput = document.getElementById('license-key-input');
        
        if (clearButton && licenseKeyInput) {
            const hasValue = licenseKeyInput.value.length > 0;
            clearButton.style.opacity = hasValue ? '1' : '0';
            clearButton.style.visibility = hasValue ? 'visible' : 'hidden';
        }
    }

    /**
     * 设置公司名称输入框事件
     */
    setupCompanyNameEvents() {
        const companyNameInput = document.getElementById('company-name-input');
        
        if (companyNameInput) {
            this.addEventListener(companyNameInput, 'input', (e) => {
                this.viewModel.setProperty('companyName', e.target.value);
            });

            this.addEventListener(companyNameInput, 'blur', (e) => {
                // 清理输入值
                const cleanValue = e.target.value.trim();
                e.target.value = cleanValue;
                this.viewModel.setProperty('companyName', cleanValue);
            });
        }
    }

    /**
     * 设置联系邮箱输入框事件
     */
    setupContactEmailEvents() {
        const contactEmailInput = document.getElementById('contact-email-input');
        
        if (contactEmailInput) {
            this.addEventListener(contactEmailInput, 'input', (e) => {
                this.viewModel.setProperty('contactEmail', e.target.value);
            });

            this.addEventListener(contactEmailInput, 'blur', (e) => {
                const value = e.target.value.trim();
                e.target.value = value;
                this.viewModel.setProperty('contactEmail', value);
                
                if (value) {
                    this.viewModel.validateProperty('contactEmail', value);
                }
            });
        }
    }

    /**
     * 设置表单提交事件
     */
    setupFormSubmitEvents() {
        const form = document.querySelector('.activation-form');
        
        if (form) {
            this.addEventListener(form, 'submit', (e) => {
                e.preventDefault();
                if (this.viewModel.getProperty('canActivate')) {
                    this.viewModel.commands.activate.execute();
                }
            });
        }
    }

    /**
     * 设置按钮事件
     */
    setupButtonEvents() {
        // 激活按钮
        const activateButton = document.getElementById('activate-button');
        if (activateButton) {
            this.addEventListener(activateButton, 'click', (e) => {
                e.preventDefault();
                if (this.viewModel.commands.activate.canExecute()) {
                    this.viewModel.commands.activate.execute();
                }
            });
        }

        // 取消按钮
        const cancelButton = document.getElementById('cancel-button');
        if (cancelButton) {
            this.addEventListener(cancelButton, 'click', (e) => {
                e.preventDefault();
                this.viewModel.commands.cancel.execute();
            });
        }

        // 帮助按钮
        const helpButton = document.getElementById('help-button');
        if (helpButton) {
            this.addEventListener(helpButton, 'click', (e) => {
                e.preventDefault();
                this.viewModel.commands.help.execute();
            });
        }

        // 技术支持按钮
        const supportButton = document.getElementById('support-button');
        if (supportButton) {
            this.addEventListener(supportButton, 'click', (e) => {
                e.preventDefault();
                this.viewModel.commands.support.execute();
            });
        }
    }

    /**
     * 设置键盘事件
     */
    setupKeyboardEvents() {
        this.addEventListener(document, 'keydown', (e) => {
            this.handleGlobalKeyDown(e);
        });
    }

    /**
     * 处理全局键盘事件
     */
    handleGlobalKeyDown(e) {
        // Escape键 - 取消操作
        if (e.key === 'Escape') {
            if (this.viewModel.getProperty('isActivating')) {
                this.viewModel.commands.cancel.execute();
            }
            return;
        }

        // F1键 - 帮助
        if (e.key === 'F1') {
            e.preventDefault();
            this.viewModel.commands.help.execute();
            return;
        }

        // Enter键 - 激活
        if (e.key === 'Enter' && !e.target.matches('button')) {
            if (this.viewModel.getProperty('canActivate')) {
                e.preventDefault();
                this.viewModel.commands.activate.execute();
            }
            return;
        }

        // Ctrl+L - 清除输入
        if (e.ctrlKey && e.key.toLowerCase() === 'l') {
            e.preventDefault();
            this.viewModel.commands.clear.execute();
            return;
        }

        // Ctrl+V - 粘贴到License Key输入框
        if (e.ctrlKey && e.key.toLowerCase() === 'v' && !e.target.matches('input')) {
            const licenseKeyInput = document.getElementById('license-key-input');
            if (licenseKeyInput) {
                licenseKeyInput.focus();
            }
            return;
        }
    }

    /**
     * 设置拖拽事件
     */
    setupDragDropEvents() {
        const container = document.querySelector('.activation-container');
        const dropOverlay = document.getElementById('drop-overlay');
        
        if (container) {
            // 拖拽进入
            this.addEventListener(document, 'dragenter', (e) => {
                e.preventDefault();
                this.dragCounter++;
                
                if (this.isDraggedFileValid(e)) {
                    this.showDropOverlay();
                }
            });

            // 拖拽离开
            this.addEventListener(document, 'dragleave', (e) => {
                e.preventDefault();
                this.dragCounter--;
                
                if (this.dragCounter === 0) {
                    this.hideDropOverlay();
                }
            });

            // 拖拽悬停
            this.addEventListener(document, 'dragover', (e) => {
                e.preventDefault();
            });

            // 拖拽放置
            this.addEventListener(document, 'drop', (e) => {
                e.preventDefault();
                this.dragCounter = 0;
                this.hideDropOverlay();
                
                this.handleFileDrop(e);
            });
        }
    }

    /**
     * 检查拖拽文件是否有效
     */
    isDraggedFileValid(e) {
        const items = e.dataTransfer?.items;
        if (!items) return false;
        
        for (let item of items) {
            if (item.kind === 'file') {
                const file = item.getAsFile();
                if (file && (file.name.endsWith('.lic') || file.name.endsWith('.license'))) {
                    return true;
                }
            } else if (item.kind === 'string' && item.type === 'text/plain') {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 显示拖拽覆盖层
     */
    showDropOverlay() {
        const dropOverlay = document.getElementById('drop-overlay');
        if (dropOverlay) {
            dropOverlay.style.display = 'flex';
            dropOverlay.classList.add('active');
        }
    }

    /**
     * 隐藏拖拽覆盖层
     */
    hideDropOverlay() {
        const dropOverlay = document.getElementById('drop-overlay');
        if (dropOverlay) {
            dropOverlay.classList.remove('active');
            setTimeout(() => {
                dropOverlay.style.display = 'none';
            }, 300);
        }
    }

    /**
     * 处理文件拖拽放置
     */
    async handleFileDrop(e) {
        const files = Array.from(e.dataTransfer.files);
        const text = e.dataTransfer.getData('text/plain');
        
        // 处理文件
        for (let file of files) {
            if (file.name.endsWith('.lic') || file.name.endsWith('.license')) {
                try {
                    const content = await this.readFileAsText(file);
                    this.processLicenseFileContent(content);
                    return;
                } catch (error) {
                    console.error('读取License文件失败:', error);
                }
            }
        }
        
        // 处理文本
        if (text) {
            this.processLicenseText(text);
        }
    }

    /**
     * 读取文件为文本
     */
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file);
        });
    }

    /**
     * 处理License文件内容
     */
    processLicenseFileContent(content) {
        // 从文件内容中提取License Key
        const licenseKeyMatch = content.match(/[A-Z0-9]{20}/);
        if (licenseKeyMatch) {
            const licenseKey = licenseKeyMatch[0];
            const formatted = licenseKey.replace(/(.{4})/g, '$1-').replace(/-$/, '');
            
            this.viewModel.setProperty('licenseKey', formatted);
            
            // 更新输入框
            const licenseKeyInput = document.getElementById('license-key-input');
            if (licenseKeyInput) {
                licenseKeyInput.value = formatted;
            }
        }
    }

    /**
     * 处理License文本
     */
    processLicenseText(text) {
        // 提取可能的License Key
        const cleanText = text.replace(/[^A-Z0-9]/g, '');
        if (cleanText.length === 20) {
            const formatted = cleanText.replace(/(.{4})/g, '$1-').replace(/-$/, '');
            this.viewModel.setProperty('licenseKey', formatted);
            
            // 更新输入框
            const licenseKeyInput = document.getElementById('license-key-input');
            if (licenseKeyInput) {
                licenseKeyInput.value = formatted;
            }
        }
    }

    /**
     * 设置窗口事件
     */
    setupWindowEvents() {
        // 窗口大小变化
        this.addEventListener(window, 'resize', Utils.debounce(() => {
            this.handleWindowResize();
        }, 250));

        // 页面可见性变化
        this.addEventListener(document, 'visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // 页面卸载前
        this.addEventListener(window, 'beforeunload', (e) => {
            this.handleBeforeUnload(e);
        });
    }

    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        // 更新布局
        this.updateLayout();
    }

    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时暂停动画
            this.pauseAnimations();
        } else {
            // 页面显示时恢复动画
            this.resumeAnimations();
        }
    }

    /**
     * 处理页面卸载前
     */
    handleBeforeUnload(e) {
        if (this.viewModel.getProperty('isActivating')) {
            e.preventDefault();
            e.returnValue = '正在激活License，确定要离开吗？';
            return e.returnValue;
        }
    }

    /**
     * 显示输入提示
     */
    showInputHint(message) {
        // 实现输入提示显示逻辑
        console.log('提示:', message);
    }

    /**
     * 隐藏输入提示
     */
    hideInputHint() {
        // 实现输入提示隐藏逻辑
    }

    /**
     * 更新布局
     */
    updateLayout() {
        // 实现布局更新逻辑
    }

    /**
     * 暂停动画
     */
    pauseAnimations() {
        document.body.style.animationPlayState = 'paused';
    }

    /**
     * 恢复动画
     */
    resumeAnimations() {
        document.body.style.animationPlayState = 'running';
    }

    /**
     * 添加事件监听器
     */
    addEventListener(element, event, handler, options = {}) {
        element.addEventListener(event, handler, options);
        
        // 记录事件监听器以便清理
        const key = `${element.constructor.name}-${event}`;
        if (!this.eventListeners.has(key)) {
            this.eventListeners.set(key, []);
        }
        this.eventListeners.get(key).push({ element, event, handler, options });
    }

    /**
     * 销毁事件处理器
     */
    destroy() {
        // 移除所有事件监听器
        for (let [key, listeners] of this.eventListeners) {
            listeners.forEach(({ element, event, handler, options }) => {
                element.removeEventListener(event, handler, options);
            });
        }
        
        this.eventListeners.clear();
        this.isInitialized = false;
        console.log('🎮 事件处理器已销毁');
    }
}
