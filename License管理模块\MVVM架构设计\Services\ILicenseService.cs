using System;
using System.Threading;
using System.Threading.Tasks;
using AirConditioningMonitor.License.Models;

namespace AirConditioningMonitor.License.Services
{
    /// <summary>
    /// License服务接口
    /// 定义License激活、验证、管理的核心功能
    /// </summary>
    public interface ILicenseService
    {
        #region License激活相关

        /// <summary>
        /// 异步激活License
        /// </summary>
        /// <param name="request">激活请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>激活响应</returns>
        Task<ActivationResponse> ActivateLicenseAsync(ActivationRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 验证License Key格式
        /// </summary>
        /// <param name="licenseKey">License Key</param>
        /// <returns>验证结果</returns>
        ValidationResult ValidateLicenseKeyFormat(string licenseKey);

        /// <summary>
        /// 取消正在进行的激活操作
        /// </summary>
        void CancelActivation();

        /// <summary>
        /// 检查License Key是否已被使用
        /// </summary>
        /// <param name="licenseKey">License Key</param>
        /// <returns>是否已被使用</returns>
        Task<bool> IsLicenseKeyUsedAsync(string licenseKey);

        #endregion

        #region License状态管理

        /// <summary>
        /// 获取当前License信息
        /// </summary>
        /// <returns>License信息，如果未激活则返回null</returns>
        Task<LicenseInfo?> GetCurrentLicenseAsync();

        /// <summary>
        /// 检查License是否有效
        /// </summary>
        /// <returns>是否有效</returns>
        Task<bool> IsLicenseValidAsync();

        /// <summary>
        /// 获取License状态
        /// </summary>
        /// <returns>License状态信息</returns>
        Task<LicenseStatusInfo> GetLicenseStatusAsync();

        /// <summary>
        /// 刷新License状态（从服务器同步）
        /// </summary>
        /// <returns>刷新后的License信息</returns>
        Task<LicenseInfo?> RefreshLicenseStatusAsync();

        #endregion

        #region 权限管理

        /// <summary>
        /// 获取当前用户权限级别
        /// </summary>
        /// <returns>权限级别</returns>
        Task<PermissionLevel> GetCurrentPermissionLevelAsync();

        /// <summary>
        /// 检查是否具有指定权限
        /// </summary>
        /// <param name="requiredPermission">所需权限级别</param>
        /// <returns>是否具有权限</returns>
        Task<bool> HasPermissionAsync(PermissionLevel requiredPermission);

        /// <summary>
        /// 获取可用功能列表
        /// </summary>
        /// <returns>功能权限映射</returns>
        Task<Dictionary<string, bool>> GetAvailableFeaturesAsync();

        #endregion

        #region License续期

        /// <summary>
        /// 检查License是否需要续期
        /// </summary>
        /// <returns>续期信息</returns>
        Task<RenewalInfo> CheckRenewalRequiredAsync();

        /// <summary>
        /// 获取续期选项
        /// </summary>
        /// <returns>续期选项列表</returns>
        Task<List<RenewalOption>> GetRenewalOptionsAsync();

        #endregion

        #region 离线支持

        /// <summary>
        /// 生成离线激活请求文件
        /// </summary>
        /// <param name="request">激活请求</param>
        /// <returns>离线请求文件路径</returns>
        Task<string> GenerateOfflineActivationRequestAsync(ActivationRequest request);

        /// <summary>
        /// 导入离线激活响应文件
        /// </summary>
        /// <param name="responseFilePath">响应文件路径</param>
        /// <returns>激活结果</returns>
        Task<ActivationResponse> ImportOfflineActivationResponseAsync(string responseFilePath);

        /// <summary>
        /// 检查是否支持离线模式
        /// </summary>
        /// <returns>是否支持离线</returns>
        bool SupportsOfflineMode();

        #endregion

        #region 事件

        /// <summary>
        /// License状态变更事件
        /// </summary>
        event EventHandler<LicenseStatusChangedEventArgs> LicenseStatusChanged;

        /// <summary>
        /// License即将到期事件
        /// </summary>
        event EventHandler<LicenseExpiringEventArgs> LicenseExpiring;

        /// <summary>
        /// 激活进度变更事件
        /// </summary>
        event EventHandler<ActivationProgressEventArgs> ActivationProgressChanged;

        #endregion
    }

    /// <summary>
    /// License状态信息
    /// </summary>
    public class LicenseStatusInfo
    {
        /// <summary>
        /// 是否已激活
        /// </summary>
        public bool IsActivated { get; set; }

        /// <summary>
        /// License信息
        /// </summary>
        public LicenseInfo? LicenseInfo { get; set; }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage { get; set; } = string.Empty;

        /// <summary>
        /// 最后检查时间
        /// </summary>
        public DateTime LastCheckTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否需要在线验证
        /// </summary>
        public bool RequiresOnlineValidation { get; set; }

        /// <summary>
        /// 下次验证时间
        /// </summary>
        public DateTime NextValidationTime { get; set; }
    }

    /// <summary>
    /// 续期信息
    /// </summary>
    public class RenewalInfo
    {
        /// <summary>
        /// 是否需要续期
        /// </summary>
        public bool IsRenewalRequired { get; set; }

        /// <summary>
        /// 到期日期
        /// </summary>
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// 剩余天数
        /// </summary>
        public int RemainingDays { get; set; }

        /// <summary>
        /// 续期紧急程度
        /// </summary>
        public RenewalUrgency Urgency { get; set; }

        /// <summary>
        /// 续期消息
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 续期选项
    /// </summary>
    public class RenewalOption
    {
        /// <summary>
        /// 选项ID
        /// </summary>
        public string OptionId { get; set; } = string.Empty;

        /// <summary>
        /// 选项名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 续期时长（月）
        /// </summary>
        public int DurationMonths { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 货币单位
        /// </summary>
        public string Currency { get; set; } = "CNY";

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否推荐
        /// </summary>
        public bool IsRecommended { get; set; }
    }

    /// <summary>
    /// 续期紧急程度
    /// </summary>
    public enum RenewalUrgency
    {
        None,       // 无需续期
        Low,        // 低优先级（30天以上）
        Medium,     // 中等优先级（7-30天）
        High,       // 高优先级（1-7天）
        Critical    // 紧急（已过期或即将过期）
    }

    #region 事件参数类

    /// <summary>
    /// License状态变更事件参数
    /// </summary>
    public class LicenseStatusChangedEventArgs : EventArgs
    {
        public LicenseStatus OldStatus { get; set; }
        public LicenseStatus NewStatus { get; set; }
        public LicenseInfo? LicenseInfo { get; set; }
        public DateTime ChangeTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// License即将到期事件参数
    /// </summary>
    public class LicenseExpiringEventArgs : EventArgs
    {
        public LicenseInfo LicenseInfo { get; set; } = new();
        public int RemainingDays { get; set; }
        public RenewalUrgency Urgency { get; set; }
        public DateTime NotificationTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 激活进度变更事件参数
    /// </summary>
    public class ActivationProgressEventArgs : EventArgs
    {
        public int ProgressPercentage { get; set; }
        public string StatusMessage { get; set; } = string.Empty;
        public ActivationStep CurrentStep { get; set; }
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 激活步骤枚举
    /// </summary>
    public enum ActivationStep
    {
        Validating,     // 验证License Key
        Connecting,     // 连接服务器
        Authenticating, // 身份验证
        Activating,     // 激活处理
        Finalizing,     // 完成激活
        Completed       // 激活完成
    }

    #endregion
}
