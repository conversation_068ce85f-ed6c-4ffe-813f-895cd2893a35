/* 
 * 主题样式 - License激活页面
 * 明暗主题切换和高对比度模式
 */

/* 主题切换动画 */
* {
  transition: background-color var(--duration-normal) var(--easing-ease-out),
              color var(--duration-normal) var(--easing-ease-out),
              border-color var(--duration-normal) var(--easing-ease-out);
}

/* 明亮主题（默认） */
[data-theme="light"] {
  /* 主题已在design-system.css中定义 */
}

/* 暗色主题 */
[data-theme="dark"] {
  /* 色彩覆盖已在design-system.css中定义 */
}

/* 暗色主题特定样式 */
[data-theme="dark"] .license-activation-page {
  background: linear-gradient(135deg, #1F1F1F 0%, #2D2D2D 100%);
}

[data-theme="dark"] .activation-header {
  background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
}

[data-theme="dark"] .activation-container {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .button-primary {
  box-shadow: 0 2px 8px rgba(96, 205, 255, 0.3);
}

[data-theme="dark"] .button-primary:hover:not(:disabled) {
  box-shadow: 0 4px 16px rgba(96, 205, 255, 0.4);
}

[data-theme="dark"] .input-license-key:focus,
[data-theme="dark"] .input-text:focus {
  box-shadow: 0 0 0 1px var(--border-focus), 
              0 0 8px rgba(96, 205, 255, 0.3);
}

[data-theme="dark"] .notification {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  /* 明亮高对比度 */
  :root {
    --primary-color: #0000FF;
    --success-color: #008000;
    --error-color: #FF0000;
    --warning-color: #FFA500;
    --text-primary: #000000;
    --background-primary: #FFFFFF;
    --border-primary: #000000;
  }
  
  .button-primary {
    border: 3px solid var(--text-primary);
    font-weight: 700;
  }
  
  .input-license-key,
  .input-text {
    border: 2px solid var(--text-primary);
    font-weight: 500;
  }
  
  .activation-status {
    border: 2px solid var(--text-primary);
  }
  
  .icon {
    filter: contrast(2);
  }
}

@media (prefers-contrast: high) and (prefers-color-scheme: dark) {
  /* 暗色高对比度 */
  [data-theme="dark"] {
    --primary-color: #00FFFF;
    --success-color: #00FF00;
    --error-color: #FF0000;
    --warning-color: #FFFF00;
    --text-primary: #FFFFFF;
    --background-primary: #000000;
    --border-primary: #FFFFFF;
  }
}

/* 系统主题检测 */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    /* 如果用户没有手动设置主题，跟随系统 */
    --primary-color: #60CDFF;
    --primary-hover: #4FC3F7;
    --primary-pressed: #29B6F6;
    
    --text-primary: #FFFFFF;
    --text-secondary: #C8C6C4;
    --text-tertiary: #A19F9D;
    --text-disabled: #605E5C;
    --text-placeholder: #8A8886;
    
    --background-primary: #1F1F1F;
    --background-secondary: #2D2D2D;
    --background-tertiary: #404040;
    --background-quaternary: #4A4A4A;
    
    --border-primary: #484644;
    --border-secondary: #3B3A39;
    --border-focus: #60CDFF;
    --divider-color: #484644;
    
    --success-background: #1A3A1A;
    --warning-background: #3A2A1A;
    --error-background: #3A1A1A;
    --info-background: #1A2A3A;
  }
}

/* 主题切换按钮（如果需要） */
.theme-toggle {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--background-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease-out);
  z-index: var(--z-index-content);
}

.theme-toggle:hover {
  background: var(--background-tertiary);
  box-shadow: var(--elevation-2);
}

.theme-toggle .icon {
  color: var(--text-secondary);
  transition: all var(--duration-fast) var(--easing-ease-out);
}

.theme-toggle:hover .icon {
  color: var(--text-primary);
}

/* 主题切换动画 */
.theme-toggle .icon-sun {
  opacity: 1;
  transform: rotate(0deg);
}

.theme-toggle .icon-moon {
  opacity: 0;
  transform: rotate(180deg);
  position: absolute;
}

[data-theme="dark"] .theme-toggle .icon-sun {
  opacity: 0;
  transform: rotate(180deg);
}

[data-theme="dark"] .theme-toggle .icon-moon {
  opacity: 1;
  transform: rotate(0deg);
}

/* 色彩模式切换动画 */
@keyframes theme-transition {
  0% {
    filter: brightness(1) contrast(1);
  }
  50% {
    filter: brightness(0.8) contrast(1.2);
  }
  100% {
    filter: brightness(1) contrast(1);
  }
}

.theme-transitioning {
  animation: theme-transition var(--duration-normal) var(--easing-ease-out);
}

/* 强制颜色模式（Windows高对比度主题） */
@media (forced-colors: active) {
  .activation-container {
    border: 1px solid ButtonText;
    background: ButtonFace;
  }
  
  .button-primary {
    background: Highlight;
    color: HighlightText;
    border: 1px solid ButtonText;
  }
  
  .button-secondary {
    background: ButtonFace;
    color: ButtonText;
    border: 1px solid ButtonText;
  }
  
  .input-license-key,
  .input-text {
    background: Field;
    color: FieldText;
    border: 1px solid ButtonText;
  }
  
  .input-license-key:focus,
  .input-text:focus {
    border: 2px solid Highlight;
  }
  
  .activation-status {
    background: ButtonFace;
    border: 1px solid ButtonText;
  }
  
  .status-icon.success {
    color: GrayText;
  }
  
  .status-icon.error {
    color: GrayText;
  }
  
  .notification {
    background: ButtonFace;
    border: 1px solid ButtonText;
    color: ButtonText;
  }
}

/* 打印模式主题 */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .activation-container {
    border: 1px solid black;
  }
  
  .button-primary,
  .button-secondary {
    border: 1px solid black;
    background: white;
    color: black;
  }
  
  .input-license-key,
  .input-text {
    border: 1px solid black;
    background: white;
  }
}

/* 用户偏好：减少透明度 */
@media (prefers-reduced-transparency: reduce) {
  .drop-overlay {
    backdrop-filter: none;
    background-color: rgba(0, 120, 212, 0.3);
  }
  
  .modal-container {
    backdrop-filter: none;
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .notification {
    backdrop-filter: none;
  }
}

/* 环境光感应（实验性） */
@media (light-level: dim) {
  :root {
    /* 在昏暗环境中自动降低亮度 */
    --background-primary: #F8F8F8;
    --text-primary: #2D2D2D;
  }
}

@media (light-level: washed) {
  :root {
    /* 在强光环境中增加对比度 */
    --text-primary: #000000;
    --border-primary: #CCCCCC;
  }
}

/* 自定义主题变量（用于JavaScript控制） */
.custom-theme {
  /* 可通过JavaScript动态设置的自定义主题 */
}

/* 节日主题（可选） */
.holiday-theme {
  --primary-color: #D2691E;
  --accent-color: #FF6347;
}

.holiday-theme .activation-title .icon-hero {
  animation: holiday-glow 2s ease-in-out infinite alternate;
}

@keyframes holiday-glow {
  from {
    filter: drop-shadow(0 0 5px var(--primary-color));
  }
  to {
    filter: drop-shadow(0 0 15px var(--accent-color));
  }
}

/* 企业主题（可选） */
.enterprise-theme {
  --primary-color: #003366;
  --secondary-color: #0066CC;
  --accent-color: #FF9900;
}

/* 无障碍主题增强 */
.accessibility-enhanced {
  --font-weight-body: 500;
  --font-weight-h1: 700;
  --font-weight-h2: 700;
  --font-weight-button: 700;
  --border-radius-medium: 8px;
  --border-radius-large: 12px;
}

.accessibility-enhanced .button-primary,
.accessibility-enhanced .button-secondary {
  min-height: 48px;
  font-size: 16px;
}

.accessibility-enhanced .input-license-key,
.accessibility-enhanced .input-text {
  min-height: 48px;
  font-size: 16px;
}
