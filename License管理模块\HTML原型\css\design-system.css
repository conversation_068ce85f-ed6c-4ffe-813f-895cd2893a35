/* 
 * Fluent Design System - 设计系统基础样式
 * 商用空调监控调试软件 - License激活页面
 * 基于Microsoft Fluent Design System规范
 */

/* CSS自定义属性 - 设计令牌 */
:root {
  /* 色彩系统 - 主色彩 */
  --primary-color: #0078D4;
  --primary-hover: #106EBE;
  --primary-pressed: #005A9E;
  --primary-disabled: #A6A6A6;
  
  --secondary-color: #40E0D0;
  --secondary-hover: #36C7B8;
  --secondary-pressed: #2CA89A;
  
  --accent-color: #FF8C00;
  --accent-hover: #E67E00;
  --accent-pressed: #CC7000;
  
  /* 功能色彩 */
  --success-color: #107C10;
  --success-background: #DFF6DD;
  --success-border: #9FD89F;
  
  --warning-color: #FF8C00;
  --warning-background: #FFF4CE;
  --warning-border: #FFCC80;
  
  --error-color: #D13438;
  --error-background: #FDE7E9;
  --error-border: #F1AEB5;
  
  --info-color: #0078D4;
  --info-background: #E1F5FE;
  --info-border: #81D4FA;
  
  /* 中性色彩 */
  --text-primary: #323130;
  --text-secondary: #605E5C;
  --text-tertiary: #8A8886;
  --text-disabled: #A19F9D;
  --text-placeholder: #C8C6C4;
  
  --background-primary: #FFFFFF;
  --background-secondary: #FAFAFA;
  --background-tertiary: #F3F2F1;
  --background-quaternary: #EDEBE9;
  
  --border-primary: #EDEBE9;
  --border-secondary: #E1DFDD;
  --border-focus: #0078D4;
  --divider-color: #EDEBE9;
  
  /* 阴影色彩 */
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.16);
  --shadow-heavy: rgba(0, 0, 0, 0.24);
  
  /* 字体系统 */
  --font-family-primary: 'Segoe UI', 'Microsoft YaHei UI', 'Microsoft YaHei', 
                         'PingFang SC', 'Hiragino Sans GB', sans-serif;
  --font-family-monospace: 'Cascadia Code', 'Consolas', 'Courier New', 
                           'SF Mono', 'Monaco', monospace;
  --font-family-numeric: 'Segoe UI', 'Roboto Mono', 'SF Pro Display', 
                         'Microsoft YaHei UI', sans-serif;
  
  /* 字体尺寸 */
  --font-size-h1: 28px;
  --font-size-h2: 20px;
  --font-size-h3: 16px;
  --font-size-h4: 14px;
  --font-size-body-large: 16px;
  --font-size-body: 14px;
  --font-size-body-small: 12px;
  --font-size-license-key: 16px;
  --font-size-button: 14px;
  --font-size-status: 14px;
  --font-size-error: 12px;
  --font-size-placeholder: 14px;
  
  /* 字体粗细 */
  --font-weight-h1: 600;
  --font-weight-h2: 600;
  --font-weight-h3: 600;
  --font-weight-h4: 600;
  --font-weight-body-large: 400;
  --font-weight-body: 400;
  --font-weight-body-small: 400;
  --font-weight-license-key: 500;
  --font-weight-button: 600;
  --font-weight-status: 500;
  --font-weight-error: 400;
  --font-weight-placeholder: 400;
  
  /* 行高 */
  --line-height-h1: 36px;
  --line-height-h2: 28px;
  --line-height-h3: 22px;
  --line-height-h4: 20px;
  --line-height-body-large: 24px;
  --line-height-body: 20px;
  --line-height-body-small: 16px;
  --line-height-license-key: 22px;
  --line-height-button: 20px;
  --line-height-status: 20px;
  --line-height-error: 16px;
  --line-height-placeholder: 20px;
  
  /* 字符间距 */
  --letter-spacing-h1: -0.5px;
  --letter-spacing-h2: -0.25px;
  --letter-spacing-h3: 0px;
  --letter-spacing-h4: 0px;
  --letter-spacing-body-large: 0px;
  --letter-spacing-body: 0px;
  --letter-spacing-body-small: 0px;
  --letter-spacing-license-key: 2px;
  --letter-spacing-button: 0px;
  --letter-spacing-status: 0px;
  --letter-spacing-error: 0px;
  --letter-spacing-placeholder: 0px;
  
  /* 图标尺寸 */
  --icon-size-small: 16px;
  --icon-size-medium: 20px;
  --icon-size-large: 24px;
  --icon-size-extra-large: 32px;
  --icon-size-hero: 48px;
  
  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* 内容宽度 */
  --content-width-max: 480px;
  --content-width-min: 320px;
  --content-width-optimal: 400px;
  
  /* 容器边距 */
  --container-padding: var(--spacing-lg);
  --container-margin: var(--spacing-md);
  
  /* Z-index层级 */
  --z-index-base: 0;
  --z-index-content: 1;
  --z-index-overlay: 10;
  --z-index-modal: 100;
  --z-index-tooltip: 1000;
  --z-index-notification: 9999;
  
  /* 阴影层次 */
  --elevation-0: none;
  --elevation-1: 0 1px 2px rgba(0, 0, 0, 0.1);
  --elevation-2: 0 2px 4px rgba(0, 0, 0, 0.12);
  --elevation-3: 0 4px 8px rgba(0, 0, 0, 0.16);
  --elevation-4: 0 8px 16px rgba(0, 0, 0, 0.2);
  --elevation-5: 0 16px 32px rgba(0, 0, 0, 0.24);
  
  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --duration-loading: 1000ms;
  
  /* 缓动函数 */
  --easing-ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --easing-ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  --easing-ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* 边框半径 */
  --border-radius-small: 2px;
  --border-radius-medium: 4px;
  --border-radius-large: 6px;
  --border-radius-extra-large: 8px;
  
  /* 触控目标尺寸 */
  --touch-target-minimum: 44px;
  --touch-target-recommended: 48px;
  --touch-target-spacing: 8px;
}

/* 暗色主题 */
[data-theme="dark"] {
  --primary-color: #60CDFF;
  --primary-hover: #4FC3F7;
  --primary-pressed: #29B6F6;
  
  --text-primary: #FFFFFF;
  --text-secondary: #C8C6C4;
  --text-tertiary: #A19F9D;
  --text-disabled: #605E5C;
  --text-placeholder: #8A8886;
  
  --background-primary: #1F1F1F;
  --background-secondary: #2D2D2D;
  --background-tertiary: #404040;
  --background-quaternary: #4A4A4A;
  
  --border-primary: #484644;
  --border-secondary: #3B3A39;
  --border-focus: #60CDFF;
  --divider-color: #484644;
  
  --success-background: #1A3A1A;
  --warning-background: #3A2A1A;
  --error-background: #3A1A1A;
  --info-background: #1A2A3A;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --background-primary: #FFFFFF;
    --border-primary: #000000;
    --primary-color: #0000FF;
    --success-color: #008000;
    --error-color: #FF0000;
    --warning-color: #FFA500;
  }
  
  [data-theme="dark"] {
    --text-primary: #FFFFFF;
    --background-primary: #000000;
    --border-primary: #FFFFFF;
    --primary-color: #00FFFF;
    --success-color: #00FF00;
    --error-color: #FF0000;
    --warning-color: #FFFF00;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --duration-fast: 50ms;
    --duration-normal: 100ms;
    --duration-slow: 150ms;
    --duration-loading: 200ms;
  }
  
  * {
    animation-duration: var(--duration-fast) !important;
    transition-duration: var(--duration-fast) !important;
  }
}

/* DPI感知 */
@media (min-resolution: 144dpi) {
  :root {
    --text-primary: #2B2B2B;
    --border-primary: #D6D4D2;
  }
}

@media (min-resolution: 192dpi) {
  :root {
    --text-primary: #1F1F1F;
    --border-primary: #C8C6C4;
  }
}

/* 多层次用户模式 */
[data-user-mode="basic"] {
  --font-scale-factor: 1.125;
  --spacing-scale-factor: 1.25;
  --touch-target-minimum: 48px;
}

[data-user-mode="advanced"] {
  --font-scale-factor: 1.0;
  --spacing-scale-factor: 1.0;
}

[data-user-mode="professional"] {
  --font-scale-factor: 0.9;
  --spacing-scale-factor: 0.875;
  --line-height-compact: 1.3;
}

/* 基础重置样式 */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-body);
  line-height: var(--line-height-body);
  color: var(--text-primary);
  background-color: var(--background-primary);
  overflow-x: hidden;
}

/* 无障碍样式 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--border-radius-medium);
  z-index: var(--z-index-notification);
  font-weight: var(--font-weight-button);
}

.skip-link:focus {
  top: 6px;
}

/* 焦点指示器 */
.focus-visible,
*:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
  border-radius: var(--border-radius-medium);
}

/* 选择文本样式 */
::selection {
  background-color: var(--primary-color);
  color: white;
}

::-moz-selection {
  background-color: var(--primary-color);
  color: white;
}
